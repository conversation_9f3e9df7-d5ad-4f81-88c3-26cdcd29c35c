"""
Professional Trading Strategy
Áp dụng các nguyên tắc từ pro traders để tạo Equity Curve tăng ổn định
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import MetaTrader5 as mt5

from ..indicators.technical_indicators import TechnicalIndicators
from ..utils.logger import setup_logger
from ..strategy.gold_strategy import TradingSignal, MarketState
from ..core.mt5_client import PositionInfo

logger = setup_logger(__name__)

@dataclass
class ProfessionalSignal:
    """Enhanced signal với multiple confirmations"""
    action: str
    strength: float
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    volume: float
    confirmations: List[str]
    risk_reward_ratio: float
    market_regime: str
    reasoning: str
    timestamp: datetime

class ProfessionalStrategy:
    """
    Professional Trading Strategy dựa trên các nguyên tắc:
    1. Trend Following với Multiple Confirmations
    2. Risk Management nghiêm ngặt
    3. Position Sizing động
    4. Market Regime Adaptation
    5. Drawdown Protection
    """

    def __init__(self, config, mt5_client=None):
        self.config = config
        self.mt5_client = mt5_client
        self.indicators = TechnicalIndicators()

        # Professional Trading Rules
        self.max_risk_per_trade = 0.01  # 1% risk per trade (conservative)
        self.max_daily_risk = 0.03      # 3% max daily risk
        self.max_drawdown_limit = 0.15  # 15% max drawdown before stop
        self.min_risk_reward = 2.0      # Minimum 1:2 risk/reward
        self.trend_confirmation_required = 3  # Minimum confirmations needed

        # State tracking
        self.daily_risk_used = 0.0
        self.current_drawdown = 0.0
        self.consecutive_losses = 0
        self.last_trade_time = None
        self.equity_high_watermark = 0.0

        # Market regime tracking
        self.current_regime = "unknown"
        self.regime_confidence = 0.0

    def generate_professional_signal(self, market_state: MarketState,
                                   current_positions: List[PositionInfo],
                                   account_info: Dict) -> Optional[ProfessionalSignal]:
        """
        Generate professional trading signal với multiple layers of confirmation
        """
        try:
            # 1. Pre-flight checks
            if not self._pre_flight_checks(account_info, current_positions):
                return None

            # 2. Market regime detection
            market_regime = self._detect_market_regime(market_state)

            # 3. Trend analysis với multiple timeframes
            trend_analysis = self._analyze_trend_multiple_timeframes(market_state)

            # 4. Generate signal với confirmations
            signal = self._generate_confirmed_signal(market_state, trend_analysis, market_regime)

            if signal:
                # 5. Risk management validation
                signal = self._apply_professional_risk_management(signal, account_info)

                # 6. Final validation
                if self._final_signal_validation(signal, market_state):
                    return signal

            return None

        except Exception as e:
            logger.error(f"Error generating professional signal: {e}")
            return None

    def _pre_flight_checks(self, account_info: Dict, positions: List[PositionInfo]) -> bool:
        """
        Pre-flight checks trước khi generate signal
        """
        # Check 1: Daily risk limit
        if self.daily_risk_used >= self.max_daily_risk:
            logger.info(f"Daily risk limit reached: {self.daily_risk_used:.2%}")
            return False

        # Check 2: Drawdown limit
        current_equity = account_info.get('equity', 0)
        if self.equity_high_watermark == 0:
            self.equity_high_watermark = current_equity
        else:
            self.equity_high_watermark = max(self.equity_high_watermark, current_equity)

        self.current_drawdown = (self.equity_high_watermark - current_equity) / self.equity_high_watermark

        if self.current_drawdown >= self.max_drawdown_limit:
            logger.warning(f"Drawdown limit reached: {self.current_drawdown:.2%}")
            return False

        # Check 3: Consecutive losses protection
        if self.consecutive_losses >= 5:
            logger.info(f"Too many consecutive losses: {self.consecutive_losses}")
            return False

        # Check 4: Maximum positions
        if len(positions) >= 2:  # Conservative position limit
            return False

        # Check 5: Time-based filters
        if not self._is_good_trading_time():
            return False

        return True

    def _detect_market_regime(self, market_state: MarketState) -> Dict:
        """
        Detect current market regime để adapt strategy
        """
        try:
            # Get recent price data for regime analysis
            data = self._get_recent_data(periods=100)
            if data is None or len(data) < 50:
                return {"regime": "unknown", "confidence": 0.0}

            # Calculate regime indicators
            sma_20 = data['close'].rolling(20).mean()
            sma_50 = data['close'].rolling(50).mean()
            atr = data['high'].subtract(data['low']).rolling(14).mean()

            current_price = data['close'].iloc[-1]
            volatility = atr.iloc[-1] / current_price

            # Trend strength
            trend_strength = abs(sma_20.iloc[-1] - sma_50.iloc[-1]) / current_price

            # Determine regime
            if trend_strength > 0.02 and volatility < 0.015:
                if sma_20.iloc[-1] > sma_50.iloc[-1]:
                    regime = "trending_bull"
                else:
                    regime = "trending_bear"
                confidence = min(0.9, trend_strength * 20)
            elif volatility > 0.025:
                regime = "high_volatility"
                confidence = min(0.8, volatility * 20)
            else:
                regime = "ranging"
                confidence = 0.6

            self.current_regime = regime
            self.regime_confidence = confidence

            return {
                "regime": regime,
                "confidence": confidence,
                "trend_strength": trend_strength,
                "volatility": volatility
            }

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return {"regime": "unknown", "confidence": 0.0}

    def _analyze_trend_multiple_timeframes(self, market_state: MarketState) -> Dict:
        """
        Analyze trend across multiple timeframes for confirmation
        """
        try:
            timeframes = [5, 15, 60]  # 5min, 15min, 1hour
            trend_analysis = {}

            for tf in timeframes:
                data = self._get_recent_data(timeframe=tf, periods=50)
                if data is None or len(data) < 20:
                    continue

                # Calculate trend indicators
                ema_fast = data['close'].ewm(span=8).mean()
                ema_slow = data['close'].ewm(span=21).mean()

                # Trend direction
                if ema_fast.iloc[-1] > ema_slow.iloc[-1]:
                    trend_direction = "bullish"
                else:
                    trend_direction = "bearish"

                # Trend strength
                trend_strength = abs(ema_fast.iloc[-1] - ema_slow.iloc[-1]) / data['close'].iloc[-1]

                trend_analysis[f"tf_{tf}"] = {
                    "direction": trend_direction,
                    "strength": trend_strength
                }

            # Overall trend consensus
            bullish_count = sum(1 for tf_data in trend_analysis.values()
                              if tf_data["direction"] == "bullish")
            bearish_count = len(trend_analysis) - bullish_count

            if bullish_count >= 2:
                overall_trend = "bullish"
                trend_confidence = bullish_count / len(trend_analysis)
            elif bearish_count >= 2:
                overall_trend = "bearish"
                trend_confidence = bearish_count / len(trend_analysis)
            else:
                overall_trend = "neutral"
                trend_confidence = 0.5

            return {
                "timeframe_analysis": trend_analysis,
                "overall_trend": overall_trend,
                "trend_confidence": trend_confidence
            }

        except Exception as e:
            logger.error(f"Error analyzing trend: {e}")
            return {"overall_trend": "neutral", "trend_confidence": 0.0}

    def _get_recent_data(self, timeframe: int = 5, periods: int = 100) -> Optional[pd.DataFrame]:
        """Get recent market data for analysis"""
        try:
            if self.mt5_client:
                return self.mt5_client.get_historical_data(
                    symbol=self.config.mt5.symbol,
                    timeframe=timeframe,
                    count=periods
                )
            return None
        except Exception as e:
            logger.error(f"Error getting recent data: {e}")
            return None

    def _is_good_trading_time(self) -> bool:
        """Check if current time is good for trading"""
        now = datetime.now()
        hour = now.hour

        # Avoid trading during low liquidity hours
        # Avoid 22:00-02:00 (low liquidity)
        if 22 <= hour or hour <= 2:
            return False

        # Avoid trading too close to previous trade
        if self.last_trade_time:
            time_since_last = now - self.last_trade_time
            if time_since_last < timedelta(minutes=30):  # Minimum 30 minutes between trades
                return False

        return True

    def _generate_confirmed_signal(self, market_state: MarketState,
                                 trend_analysis: Dict,
                                 market_regime: Dict) -> Optional[ProfessionalSignal]:
        """
        Generate signal với multiple confirmations
        """
        try:
            confirmations = []
            signal_strength = 0.0

            # 1. Trend confirmation
            overall_trend = trend_analysis.get("overall_trend", "neutral")
            trend_confidence = trend_analysis.get("trend_confidence", 0.0)

            if overall_trend != "neutral" and trend_confidence >= 0.67:
                confirmations.append(f"Multi-timeframe trend: {overall_trend}")
                signal_strength += 0.3

            # 2. MACD confirmation
            if hasattr(market_state, 'macd_signal'):
                macd = market_state.macd_signal
                if macd.crossover in ['bullish_cross', 'bearish_cross']:
                    confirmations.append(f"MACD crossover: {macd.crossover}")
                    signal_strength += 0.25
                elif macd.trend in ['bullish', 'bearish'] and macd.trend == overall_trend:
                    confirmations.append(f"MACD trend alignment: {macd.trend}")
                    signal_strength += 0.15

            # 3. RSI confirmation (avoid overbought/oversold extremes)
            if hasattr(market_state, 'rsi_data'):
                rsi = market_state.rsi_data.rsi
                if overall_trend == "bullish" and 30 < rsi < 70:
                    confirmations.append("RSI in healthy bullish range")
                    signal_strength += 0.15
                elif overall_trend == "bearish" and 30 < rsi < 70:
                    confirmations.append("RSI in healthy bearish range")
                    signal_strength += 0.15

            # 4. Bollinger Bands confirmation
            if hasattr(market_state, 'bb_data'):
                bb = market_state.bb_data
                current_price = market_state.price

                # Avoid trading at extreme bands
                if bb.lower_band < current_price < bb.upper_band:
                    confirmations.append("Price within BB bands")
                    signal_strength += 0.1

            # 5. Market regime confirmation
            regime = market_regime.get("regime", "unknown")
            regime_confidence = market_regime.get("confidence", 0.0)

            if regime in ["trending_bull", "trending_bear"] and regime_confidence > 0.7:
                if (regime == "trending_bull" and overall_trend == "bullish") or \
                   (regime == "trending_bear" and overall_trend == "bearish"):
                    confirmations.append(f"Market regime alignment: {regime}")
                    signal_strength += 0.2

            # Check minimum confirmations and strength
            if len(confirmations) < self.trend_confirmation_required or signal_strength < 0.6:
                return None

            # Determine signal direction
            if overall_trend == "bullish" and signal_strength >= 0.6:
                action = "buy"
                entry_price = market_state.ask
            elif overall_trend == "bearish" and signal_strength >= 0.6:
                action = "sell"
                entry_price = market_state.bid
            else:
                return None

            # Calculate stop loss and take profit
            atr = getattr(market_state, 'atr_data', None)
            if not atr:
                return None

            atr_value = atr.atr

            if action == "buy":
                stop_loss = entry_price - (atr_value * 1.5)  # Tighter stop loss
                take_profit = entry_price + (atr_value * 3.0)  # 1:2 risk/reward
            else:
                stop_loss = entry_price + (atr_value * 1.5)
                take_profit = entry_price - (atr_value * 3.0)

            # Calculate risk/reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0

            # Ensure minimum risk/reward ratio
            if risk_reward_ratio < self.min_risk_reward:
                return None

            return ProfessionalSignal(
                action=action,
                strength=signal_strength,
                confidence=trend_confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                volume=0.01,  # Will be calculated by risk management
                confirmations=confirmations,
                risk_reward_ratio=risk_reward_ratio,
                market_regime=regime,
                reasoning=f"Professional signal: {', '.join(confirmations)}",
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error generating confirmed signal: {e}")
            return None

    def _apply_professional_risk_management(self, signal: ProfessionalSignal,
                                          account_info: Dict) -> Optional[ProfessionalSignal]:
        """
        Apply professional risk management rules
        """
        try:
            account_balance = account_info.get('balance', 0)
            if account_balance <= 0:
                return None

            # Calculate position size based on risk
            risk_amount = account_balance * self.max_risk_per_trade

            # Adjust risk based on consecutive losses
            if self.consecutive_losses > 0:
                risk_reduction = min(0.5, self.consecutive_losses * 0.1)
                risk_amount *= (1 - risk_reduction)

            # Adjust risk based on signal confidence
            risk_amount *= signal.confidence

            # Adjust risk based on market regime
            if signal.market_regime == "high_volatility":
                risk_amount *= 0.5  # Reduce risk in high volatility
            elif signal.market_regime in ["trending_bull", "trending_bear"]:
                risk_amount *= 1.2  # Slightly increase risk in trending markets

            # Calculate volume based on risk
            risk_per_unit = abs(signal.entry_price - signal.stop_loss)
            if risk_per_unit > 0:
                volume = risk_amount / risk_per_unit

                # Apply volume limits
                min_volume = getattr(self.config.trading, 'min_volume', 0.01)
                max_volume = getattr(self.config.trading, 'max_volume', 1.0)
                volume = max(min_volume, min(max_volume, volume))

                # Round to valid volume step
                volume_step = getattr(self.config.trading, 'volume_step', 0.01)
                volume = round(volume / volume_step) * volume_step

                signal.volume = volume

                # Update daily risk tracking
                actual_risk = volume * risk_per_unit / account_balance
                self.daily_risk_used += actual_risk

                return signal

            return None

        except Exception as e:
            logger.error(f"Error applying risk management: {e}")
            return None

    def _final_signal_validation(self, signal: ProfessionalSignal,
                               market_state: MarketState) -> bool:
        """
        Final validation before signal execution
        """
        try:
            # Check spread conditions
            if hasattr(market_state, 'spread'):
                max_spread = getattr(self.config.risk, 'max_spread', 200)
                if market_state.spread > max_spread:
                    logger.info(f"Spread too high: {market_state.spread}")
                    return False

            # Check minimum volume
            if signal.volume < getattr(self.config.trading, 'min_volume', 0.01):
                logger.info(f"Volume too small: {signal.volume}")
                return False

            # Check risk/reward ratio one more time
            if signal.risk_reward_ratio < self.min_risk_reward:
                logger.info(f"Risk/reward too low: {signal.risk_reward_ratio}")
                return False

            # Check signal strength
            if signal.strength < 0.6:
                logger.info(f"Signal strength too low: {signal.strength}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error in final validation: {e}")
            return False

    def update_performance_tracking(self, trade_result: Dict):
        """
        Update performance tracking for adaptive risk management
        """
        try:
            profit = trade_result.get('profit', 0)

            if profit > 0:
                self.consecutive_losses = 0
            else:
                self.consecutive_losses += 1

            # Reset daily risk at start of new day
            now = datetime.now()
            if self.last_trade_time and self.last_trade_time.date() != now.date():
                self.daily_risk_used = 0.0

            self.last_trade_time = now

        except Exception as e:
            logger.error(f"Error updating performance tracking: {e}")
