"""
Strategy Integration
Tích hợp Master Strategy Engine với main trading bot
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from .master_strategy_engine import MasterStrategyEngine, MasterSignal
from ..strategy.gold_strategy import TradingSignal, MarketState
from ..core.mt5_client import PositionInfo
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class StrategyIntegration:
    """
    Strategy Integration Layer để kết nối Master Strategy Engine với main bot
    """

    def __init__(self, config, mt5_client=None):
        self.config = config
        self.mt5_client = mt5_client

        # Initialize Master Strategy Engine
        self.master_engine = MasterStrategyEngine(config, mt5_client)

        # Integration settings
        self.last_signal_time = None
        self.signal_cooldown = timedelta(minutes=30)  # 30 minutes between signals
        self.performance_tracking = {
            'total_signals': 0,
            'successful_signals': 0,
            'failed_signals': 0,
            'total_profit': 0.0,
            'total_loss': 0.0
        }

    def generate_trading_signal(self, market_state: MarketState,
                              current_positions: List[PositionInfo],
                              account_info: Dict) -> Optional[TradingSignal]:
        """
        Generate trading signal using Master Strategy Engine
        Converts MasterSignal to TradingSignal for compatibility
        """
        try:
            # Check signal cooldown
            if self._is_in_cooldown():
                return None

            # Generate master signal
            master_signal = self.master_engine.generate_master_signal(
                market_state, current_positions, account_info
            )

            if not master_signal:
                return None

            # Convert to TradingSignal for compatibility
            trading_signal = self._convert_to_trading_signal(master_signal)

            if trading_signal:
                self.last_signal_time = datetime.now()
                self.performance_tracking['total_signals'] += 1

                # Log signal generation
                logger.info(f"Professional signal generated: {trading_signal.action} "
                          f"at {trading_signal.entry_price:.5f} "
                          f"with {len(master_signal.confirmations)} confirmations")

                # Log risk metrics
                logger.info(f"Risk: {master_signal.risk_percentage:.2%} "
                          f"({master_signal.risk_amount:.2f}), "
                          f"R:R = 1:{master_signal.risk_reward_ratio:.1f}")

            return trading_signal

        except Exception as e:
            logger.error(f"Error generating trading signal: {e}")
            return None

    def _is_in_cooldown(self) -> bool:
        """Check if we're in signal cooldown period"""
        if not self.last_signal_time:
            return False

        time_since_last = datetime.now() - self.last_signal_time
        return time_since_last < self.signal_cooldown

    def _convert_to_trading_signal(self, master_signal: MasterSignal) -> Optional[TradingSignal]:
        """Convert MasterSignal to TradingSignal for compatibility"""
        try:
            # Create reasoning with all confirmations
            detailed_reasoning = f"Professional Strategy: {master_signal.reasoning}\n"
            detailed_reasoning += f"Confirmations ({len(master_signal.confirmations)}):\n"
            for i, conf in enumerate(master_signal.confirmations[:5], 1):  # Show first 5
                detailed_reasoning += f"  {i}. {conf}\n"

            if len(master_signal.confirmations) > 5:
                detailed_reasoning += f"  ... and {len(master_signal.confirmations) - 5} more\n"

            detailed_reasoning += f"Market Regime: {master_signal.market_regime}\n"
            detailed_reasoning += f"Risk: {master_signal.risk_percentage:.2%} "
            detailed_reasoning += f"(R:R = 1:{master_signal.risk_reward_ratio:.1f})"

            trading_signal = TradingSignal(
                action=master_signal.action,
                strength=master_signal.strength,
                entry_price=master_signal.entry_price,
                stop_loss=master_signal.stop_loss,
                take_profit=master_signal.take_profit,
                volume=master_signal.volume,
                confidence=master_signal.confidence,
                reasoning=detailed_reasoning,
                timestamp=master_signal.timestamp
            )

            return trading_signal

        except Exception as e:
            logger.error(f"Error converting master signal: {e}")
            return None

    def update_trade_result(self, trade_result: Dict):
        """Update trade result for performance tracking"""
        try:
            profit = trade_result.get('profit', 0)

            # Update performance tracking
            if profit > 0:
                self.performance_tracking['successful_signals'] += 1
                self.performance_tracking['total_profit'] += profit
            else:
                self.performance_tracking['failed_signals'] += 1
                self.performance_tracking['total_loss'] += abs(profit)

            # Update master engine
            self.master_engine.risk_manager.update_trade_result(trade_result)

            # Log trade result
            logger.info(f"Trade result updated: Profit = {profit:.2f}")

        except Exception as e:
            logger.error(f"Error updating trade result: {e}")

    def get_strategy_status(self) -> Dict:
        """Get comprehensive strategy status"""
        try:
            # Get master engine performance
            master_performance = self.master_engine.get_strategy_performance()

            # Calculate win rate
            total_completed = (self.performance_tracking['successful_signals'] +
                             self.performance_tracking['failed_signals'])
            win_rate = (self.performance_tracking['successful_signals'] / total_completed * 100
                       if total_completed > 0 else 0)

            # Calculate profit factor
            total_profit = self.performance_tracking['total_profit']
            total_loss = self.performance_tracking['total_loss']
            profit_factor = total_profit / total_loss if total_loss > 0 else 0

            status = {
                'strategy_engine': 'Professional Master Strategy',
                'status': 'Active',
                'last_signal_time': self.last_signal_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_signal_time else 'None',
                'signal_cooldown_remaining': self._get_cooldown_remaining(),
                'performance': {
                    'total_signals': self.performance_tracking['total_signals'],
                    'successful_signals': self.performance_tracking['successful_signals'],
                    'failed_signals': self.performance_tracking['failed_signals'],
                    'win_rate': f"{win_rate:.1f}%",
                    'profit_factor': f"{profit_factor:.2f}",
                    'total_profit': f"{total_profit:.2f}",
                    'total_loss': f"{total_loss:.2f}"
                },
                'master_engine': master_performance
            }

            return status

        except Exception as e:
            logger.error(f"Error getting strategy status: {e}")
            return {'error': str(e)}

    def _get_cooldown_remaining(self) -> str:
        """Get remaining cooldown time"""
        if not self.last_signal_time:
            return "No cooldown"

        time_since_last = datetime.now() - self.last_signal_time
        if time_since_last >= self.signal_cooldown:
            return "No cooldown"

        remaining = self.signal_cooldown - time_since_last
        minutes = int(remaining.total_seconds() / 60)
        seconds = int(remaining.total_seconds() % 60)

        return f"{minutes}m {seconds}s"

    def get_market_assessment(self, market_state: MarketState) -> Dict:
        """Get current market assessment"""
        try:
            assessment = self.master_engine.get_current_market_assessment(market_state)

            # Add integration-specific info
            assessment['signal_cooldown'] = self._get_cooldown_remaining()
            assessment['strategy_active'] = True
            assessment['next_signal_allowed'] = not self._is_in_cooldown()

            return assessment

        except Exception as e:
            logger.error(f"Error getting market assessment: {e}")
            return {'error': str(e)}

    def adjust_strategy_parameters(self, market_conditions: Dict):
        """Dynamically adjust strategy parameters based on market conditions"""
        try:
            # Get current performance metrics
            risk_metrics = self.master_engine.risk_manager.risk_metrics

            # Adjust based on recent performance
            adjustments = {}

            # If too many consecutive losses, become more conservative
            if risk_metrics.consecutive_losses >= 3:
                adjustments['max_risk_per_trade'] = 0.005  # Reduce to 0.5%
                adjustments['min_confirmations'] = 5       # Require more confirmations
                logger.info("Adjusting to conservative mode due to consecutive losses")

            # If high win rate, slightly increase risk
            elif risk_metrics.win_rate > 0.7 and risk_metrics.profit_factor > 1.5:
                adjustments['max_risk_per_trade'] = 0.015  # Increase to 1.5%
                adjustments['min_confirmations'] = 3       # Allow fewer confirmations
                logger.info("Adjusting to aggressive mode due to good performance")

            # If high volatility, reduce risk
            volatility = market_conditions.get('volatility', 0.02)
            if volatility > 0.04:  # High volatility
                adjustments['max_risk_per_trade'] = 0.005  # Reduce risk
                adjustments['min_risk_reward'] = 2.5       # Higher R:R requirement
                logger.info("Adjusting for high volatility conditions")

            # Apply adjustments
            if adjustments:
                self.master_engine.update_strategy_parameters(adjustments)
                logger.info(f"Strategy parameters adjusted: {adjustments}")

        except Exception as e:
            logger.error(f"Error adjusting strategy parameters: {e}")

    def reset_daily_performance(self):
        """Reset daily performance metrics"""
        try:
            # Reset master engine daily metrics
            self.master_engine.reset_daily_metrics()

            # Reset integration performance tracking
            self.performance_tracking = {
                'total_signals': 0,
                'successful_signals': 0,
                'failed_signals': 0,
                'total_profit': 0.0,
                'total_loss': 0.0
            }

            logger.info("Daily performance metrics reset")

        except Exception as e:
            logger.error(f"Error resetting daily performance: {e}")

    def get_signal_quality_report(self) -> Dict:
        """Get detailed signal quality report"""
        try:
            master_performance = self.master_engine.get_strategy_performance()

            # Calculate signal quality metrics
            total_signals = self.performance_tracking['total_signals']
            successful_signals = self.performance_tracking['successful_signals']

            signal_quality = {
                'signal_generation_rate': f"{total_signals} signals generated",
                'signal_success_rate': f"{(successful_signals/total_signals*100):.1f}%" if total_signals > 0 else "0%",
                'average_confirmations': "4-6 confirmations per signal",
                'risk_management': "Professional level (1% per trade)",
                'position_management': "Advanced (trailing stops, partial exits)",
                'market_adaptation': "Dynamic regime detection",
                'quality_score': self._calculate_quality_score()
            }

            return {
                'signal_quality': signal_quality,
                'master_engine_stats': master_performance
            }

        except Exception as e:
            logger.error(f"Error generating signal quality report: {e}")
            return {'error': str(e)}

    def _calculate_quality_score(self) -> str:
        """Calculate overall strategy quality score"""
        try:
            risk_metrics = self.master_engine.risk_manager.risk_metrics

            score = 0

            # Win rate component (0-30 points)
            if risk_metrics.win_rate > 0.6:
                score += 30
            elif risk_metrics.win_rate > 0.5:
                score += 20
            elif risk_metrics.win_rate > 0.4:
                score += 10

            # Profit factor component (0-30 points)
            if risk_metrics.profit_factor > 1.5:
                score += 30
            elif risk_metrics.profit_factor > 1.2:
                score += 20
            elif risk_metrics.profit_factor > 1.0:
                score += 10

            # Drawdown component (0-20 points)
            if risk_metrics.current_drawdown < 0.05:
                score += 20
            elif risk_metrics.current_drawdown < 0.10:
                score += 15
            elif risk_metrics.current_drawdown < 0.15:
                score += 10

            # Consistency component (0-20 points)
            if risk_metrics.consecutive_losses < 3:
                score += 20
            elif risk_metrics.consecutive_losses < 5:
                score += 10

            # Convert to grade
            if score >= 80:
                return f"Excellent ({score}/100)"
            elif score >= 60:
                return f"Good ({score}/100)"
            elif score >= 40:
                return f"Fair ({score}/100)"
            else:
                return f"Poor ({score}/100)"

        except Exception as e:
            logger.error(f"Error calculating quality score: {e}")
            return "Unknown"
