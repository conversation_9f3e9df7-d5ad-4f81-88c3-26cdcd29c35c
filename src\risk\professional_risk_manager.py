"""
Professional Risk Manager
Á<PERSON> dụng các nguyên tắc risk management từ các pro trader
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import math

from ..utils.logger import setup_logger
from ..core.mt5_client import PositionInfo

logger = setup_logger(__name__)

@dataclass
class RiskMetrics:
    """Risk metrics tracking"""
    current_drawdown: float
    max_drawdown: float
    daily_risk_used: float
    weekly_risk_used: float
    consecutive_losses: int
    consecutive_wins: int
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    var_95: float  # Value at Risk 95%
    kelly_fraction: float

@dataclass
class PositionRisk:
    """Position risk calculation"""
    volume: float
    risk_amount: float
    risk_percentage: float
    stop_loss_distance: float
    risk_reward_ratio: float
    confidence_adjusted_volume: float

class ProfessionalRiskManager:
    """
    Professional Risk Manager với các nguyên tắc:
    1. Dynamic Position Sizing
    2. Drawdown Protection
    3. Kelly <PERSON>riterion
    4. Risk Parity
    5. Volatility Adjustment
    """

    def __init__(self, config):
        self.config = config

        # Professional Risk Parameters
        self.max_risk_per_trade = 0.01      # 1% max risk per trade
        self.max_daily_risk = 0.03          # 3% max daily risk
        self.max_weekly_risk = 0.10         # 10% max weekly risk
        self.max_drawdown_limit = 0.15      # 15% max drawdown
        self.min_risk_reward = 2.0          # Minimum 1:2 risk/reward
        self.kelly_fraction = 0.25          # Conservative Kelly fraction

        # Risk tracking
        self.risk_metrics = RiskMetrics(
            current_drawdown=0.0,
            max_drawdown=0.0,
            daily_risk_used=0.0,
            weekly_risk_used=0.0,
            consecutive_losses=0,
            consecutive_wins=0,
            win_rate=0.5,
            profit_factor=1.0,
            sharpe_ratio=0.0,
            var_95=0.0,
            kelly_fraction=0.25
        )

        # Performance tracking
        self.equity_high_watermark = 0.0
        self.trade_history = []
        self.daily_pnl = []
        self.last_reset_date = datetime.now().date()

    def calculate_position_size(self, signal_data: Dict, account_info: Dict,
                              market_volatility: float = 0.02) -> Optional[PositionRisk]:
        """
        Calculate optimal position size using multiple methods
        """
        try:
            account_balance = account_info.get('balance', 0)
            account_equity = account_info.get('equity', account_balance)

            if account_balance <= 0:
                return None

            # Update risk metrics
            self._update_risk_metrics(account_equity)

            # Check if trading is allowed
            if not self._is_trading_allowed():
                logger.info("Trading not allowed due to risk limits")
                return None

            # Get signal parameters
            entry_price = signal_data.get('entry_price', 0)
            stop_loss = signal_data.get('stop_loss', 0)
            confidence = signal_data.get('confidence', 0.5)

            if entry_price <= 0 or stop_loss <= 0:
                return None

            # Calculate stop loss distance
            stop_loss_distance = abs(entry_price - stop_loss)

            # Calculate risk/reward ratio
            take_profit = signal_data.get('take_profit', 0)
            if take_profit > 0:
                reward_distance = abs(take_profit - entry_price)
                risk_reward_ratio = reward_distance / stop_loss_distance
            else:
                risk_reward_ratio = 2.0  # Default

            # Check minimum risk/reward
            if risk_reward_ratio < self.min_risk_reward:
                logger.info(f"Risk/reward too low: {risk_reward_ratio:.2f}")
                return None

            # Calculate base risk amount
            base_risk_amount = account_balance * self.max_risk_per_trade

            # Apply dynamic adjustments
            adjusted_risk_amount = self._apply_dynamic_risk_adjustments(
                base_risk_amount, confidence, market_volatility
            )

            # Calculate volume
            volume = adjusted_risk_amount / stop_loss_distance

            # Apply volume constraints
            volume = self._apply_volume_constraints(volume)

            # Calculate final risk metrics
            final_risk_amount = volume * stop_loss_distance
            risk_percentage = final_risk_amount / account_balance

            # Confidence adjusted volume (for Kelly criterion)
            kelly_volume = self._calculate_kelly_volume(volume, confidence)

            return PositionRisk(
                volume=volume,
                risk_amount=final_risk_amount,
                risk_percentage=risk_percentage,
                stop_loss_distance=stop_loss_distance,
                risk_reward_ratio=risk_reward_ratio,
                confidence_adjusted_volume=kelly_volume
            )

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return None

    def _update_risk_metrics(self, current_equity: float):
        """Update risk metrics and drawdown tracking"""
        try:
            # Update equity high watermark
            if self.equity_high_watermark == 0:
                self.equity_high_watermark = current_equity
            else:
                self.equity_high_watermark = max(self.equity_high_watermark, current_equity)

            # Calculate current drawdown
            self.risk_metrics.current_drawdown = (
                self.equity_high_watermark - current_equity
            ) / self.equity_high_watermark

            # Update max drawdown
            self.risk_metrics.max_drawdown = max(
                self.risk_metrics.max_drawdown,
                self.risk_metrics.current_drawdown
            )

            # Reset daily/weekly risk if needed
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                # Reset daily risk
                self.risk_metrics.daily_risk_used = 0.0

                # Reset weekly risk if it's a new week
                if current_date.weekday() == 0:  # Monday
                    self.risk_metrics.weekly_risk_used = 0.0

                self.last_reset_date = current_date

        except Exception as e:
            logger.error(f"Error updating risk metrics: {e}")

    def _is_trading_allowed(self) -> bool:
        """Check if trading is allowed based on risk limits"""

        # Check drawdown limit
        if self.risk_metrics.current_drawdown >= self.max_drawdown_limit:
            logger.warning(f"Drawdown limit exceeded: {self.risk_metrics.current_drawdown:.2%}")
            return False

        # Check daily risk limit
        if self.risk_metrics.daily_risk_used >= self.max_daily_risk:
            logger.info(f"Daily risk limit reached: {self.risk_metrics.daily_risk_used:.2%}")
            return False

        # Check weekly risk limit
        if self.risk_metrics.weekly_risk_used >= self.max_weekly_risk:
            logger.info(f"Weekly risk limit reached: {self.risk_metrics.weekly_risk_used:.2%}")
            return False

        # Check consecutive losses
        if self.risk_metrics.consecutive_losses >= 5:
            logger.info(f"Too many consecutive losses: {self.risk_metrics.consecutive_losses}")
            return False

        return True

    def _apply_dynamic_risk_adjustments(self, base_risk: float,
                                      confidence: float,
                                      market_volatility: float) -> float:
        """Apply dynamic risk adjustments based on various factors"""

        adjusted_risk = base_risk

        # 1. Confidence adjustment
        confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 to 1.0
        adjusted_risk *= confidence_multiplier

        # 2. Consecutive losses adjustment
        if self.risk_metrics.consecutive_losses > 0:
            loss_reduction = min(0.5, self.risk_metrics.consecutive_losses * 0.1)
            adjusted_risk *= (1 - loss_reduction)

        # 3. Consecutive wins adjustment (slight increase)
        if self.risk_metrics.consecutive_wins >= 3:
            win_increase = min(0.2, (self.risk_metrics.consecutive_wins - 2) * 0.05)
            adjusted_risk *= (1 + win_increase)

        # 4. Volatility adjustment
        if market_volatility > 0.03:  # High volatility
            volatility_reduction = min(0.5, (market_volatility - 0.02) * 10)
            adjusted_risk *= (1 - volatility_reduction)

        # 5. Win rate adjustment
        if self.risk_metrics.win_rate < 0.4:  # Low win rate
            adjusted_risk *= 0.7
        elif self.risk_metrics.win_rate > 0.6:  # High win rate
            adjusted_risk *= 1.1

        # 6. Profit factor adjustment
        if self.risk_metrics.profit_factor < 1.0:  # Losing strategy
            adjusted_risk *= 0.5
        elif self.risk_metrics.profit_factor > 1.5:  # Profitable strategy
            adjusted_risk *= 1.2

        return adjusted_risk

    def _apply_volume_constraints(self, volume: float) -> float:
        """Apply volume constraints and rounding"""

        # Get volume limits from config
        min_volume = getattr(self.config.trading, 'min_volume', 0.01)
        max_volume = getattr(self.config.trading, 'max_volume', 1.0)
        volume_step = getattr(self.config.trading, 'volume_step', 0.01)

        # Apply limits
        volume = max(min_volume, min(max_volume, volume))

        # Round to valid step
        volume = round(volume / volume_step) * volume_step

        return volume

    def _calculate_kelly_volume(self, base_volume: float, confidence: float) -> float:
        """Calculate Kelly Criterion adjusted volume"""

        try:
            # Estimate win rate and average win/loss from recent trades
            if len(self.trade_history) < 10:
                return base_volume  # Not enough data

            recent_trades = self.trade_history[-20:]  # Last 20 trades
            wins = [t for t in recent_trades if t['profit'] > 0]
            losses = [t for t in recent_trades if t['profit'] <= 0]

            if not wins or not losses:
                return base_volume

            win_rate = len(wins) / len(recent_trades)
            avg_win = np.mean([t['profit'] for t in wins])
            avg_loss = abs(np.mean([t['profit'] for t in losses]))

            if avg_loss == 0:
                return base_volume

            # Kelly formula: f = (bp - q) / b
            # where b = odds (avg_win/avg_loss), p = win_rate, q = loss_rate
            b = avg_win / avg_loss
            p = win_rate
            q = 1 - p

            kelly_fraction = (b * p - q) / b

            # Apply conservative Kelly (25% of optimal)
            kelly_fraction *= self.kelly_fraction

            # Ensure reasonable bounds
            kelly_fraction = max(0, min(0.5, kelly_fraction))

            # Apply Kelly adjustment
            kelly_volume = base_volume * (1 + kelly_fraction)

            return kelly_volume

        except Exception as e:
            logger.error(f"Error calculating Kelly volume: {e}")
            return base_volume

    def should_close_position(self, position: PositionInfo,
                            current_price: float,
                            market_state: Dict) -> Tuple[bool, str]:
        """
        Professional position closing logic
        """
        try:
            # 1. Check trailing stop
            if self._check_trailing_stop(position, current_price):
                return True, "Trailing stop triggered"

            # 2. Check time-based exit
            position_age = datetime.now() - position.time
            max_position_time = timedelta(hours=8)  # Maximum 8 hours

            if position_age > max_position_time:
                return True, "Maximum position time exceeded"

            # 3. Check profit target scaling
            if self._check_profit_scaling(position, current_price):
                return True, "Profit scaling target reached"

            # 4. Check market regime change
            if self._check_regime_change(position, market_state):
                return True, "Market regime changed"

            # 5. Check volatility spike
            if self._check_volatility_spike(market_state):
                return True, "Volatility spike detected"

            # 6. Check drawdown protection
            if self.risk_metrics.current_drawdown >= self.max_drawdown_limit * 0.8:
                return True, "Drawdown protection activated"

            return False, "Position maintained"

        except Exception as e:
            logger.error(f"Error checking position close: {e}")
            return False, "Error in position check"

    def _check_trailing_stop(self, position: PositionInfo, current_price: float) -> bool:
        """Check trailing stop logic"""
        try:
            # Calculate current profit/loss
            if position.type == 0:  # Buy position
                current_pnl = current_price - position.price_open
            else:  # Sell position
                current_pnl = position.price_open - current_price

            # Only apply trailing stop if in profit
            if current_pnl <= 0:
                return False

            # Calculate trailing stop distance (2% of current price)
            trailing_distance = current_price * 0.02

            # Check if price moved against us beyond trailing distance
            if position.type == 0:  # Buy position
                trailing_stop_price = current_price - trailing_distance
                return position.price_open < trailing_stop_price
            else:  # Sell position
                trailing_stop_price = current_price + trailing_distance
                return position.price_open > trailing_stop_price

        except Exception as e:
            logger.error(f"Error checking trailing stop: {e}")
            return False

    def _check_profit_scaling(self, position: PositionInfo, current_price: float) -> bool:
        """Check if we should take partial profits"""
        try:
            # Calculate current profit percentage
            if position.type == 0:  # Buy position
                profit_pct = (current_price - position.price_open) / position.price_open
            else:  # Sell position
                profit_pct = (position.price_open - current_price) / position.price_open

            # Take partial profits at 2% gain
            if profit_pct >= 0.02:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking profit scaling: {e}")
            return False

    def _check_regime_change(self, position: PositionInfo, market_state: Dict) -> bool:
        """Check for significant market regime change"""
        try:
            # This would need market regime detection
            # For now, use simple volatility check
            volatility = market_state.get('volatility', 0.02)

            # Close position if volatility spikes above 5%
            if volatility > 0.05:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking regime change: {e}")
            return False

    def _check_volatility_spike(self, market_state: Dict) -> bool:
        """Check for volatility spike"""
        try:
            volatility = market_state.get('volatility', 0.02)

            # Close positions if volatility exceeds 6%
            return volatility > 0.06

        except Exception as e:
            logger.error(f"Error checking volatility spike: {e}")
            return False

    def update_trade_result(self, trade_result: Dict):
        """Update performance tracking with trade result"""
        try:
            profit = trade_result.get('profit', 0)
            volume = trade_result.get('volume', 0)

            # Add to trade history
            self.trade_history.append({
                'profit': profit,
                'volume': volume,
                'timestamp': datetime.now()
            })

            # Keep only last 100 trades
            if len(self.trade_history) > 100:
                self.trade_history = self.trade_history[-100:]

            # Update consecutive wins/losses
            if profit > 0:
                self.risk_metrics.consecutive_wins += 1
                self.risk_metrics.consecutive_losses = 0
            else:
                self.risk_metrics.consecutive_losses += 1
                self.risk_metrics.consecutive_wins = 0

            # Update daily/weekly risk used
            risk_used = abs(profit) / trade_result.get('account_balance', 1)
            self.risk_metrics.daily_risk_used += risk_used
            self.risk_metrics.weekly_risk_used += risk_used

            # Update performance metrics
            self._update_performance_metrics()

        except Exception as e:
            logger.error(f"Error updating trade result: {e}")

    def _update_performance_metrics(self):
        """Update performance metrics from trade history"""
        try:
            if len(self.trade_history) < 10:
                return

            recent_trades = self.trade_history[-50:]  # Last 50 trades
            profits = [t['profit'] for t in recent_trades]

            # Calculate win rate
            wins = [p for p in profits if p > 0]
            self.risk_metrics.win_rate = len(wins) / len(profits)

            # Calculate profit factor
            total_wins = sum(wins) if wins else 0
            losses = [p for p in profits if p <= 0]
            total_losses = abs(sum(losses)) if losses else 1

            self.risk_metrics.profit_factor = total_wins / total_losses if total_losses > 0 else 1.0

            # Calculate Sharpe ratio (simplified)
            if len(profits) > 1:
                returns_std = np.std(profits)
                avg_return = np.mean(profits)
                self.risk_metrics.sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0

            # Calculate VaR 95%
            if len(profits) >= 20:
                self.risk_metrics.var_95 = np.percentile(profits, 5)  # 5th percentile

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    def get_risk_report(self) -> Dict:
        """Get comprehensive risk report"""
        return {
            'current_drawdown': f"{self.risk_metrics.current_drawdown:.2%}",
            'max_drawdown': f"{self.risk_metrics.max_drawdown:.2%}",
            'daily_risk_used': f"{self.risk_metrics.daily_risk_used:.2%}",
            'weekly_risk_used': f"{self.risk_metrics.weekly_risk_used:.2%}",
            'consecutive_losses': self.risk_metrics.consecutive_losses,
            'consecutive_wins': self.risk_metrics.consecutive_wins,
            'win_rate': f"{self.risk_metrics.win_rate:.1%}",
            'profit_factor': f"{self.risk_metrics.profit_factor:.2f}",
            'sharpe_ratio': f"{self.risk_metrics.sharpe_ratio:.2f}",
            'var_95': f"{self.risk_metrics.var_95:.2f}",
            'kelly_fraction': f"{self.risk_metrics.kelly_fraction:.2f}",
            'trading_allowed': self._is_trading_allowed(),
            'total_trades': len(self.trade_history)
        }
