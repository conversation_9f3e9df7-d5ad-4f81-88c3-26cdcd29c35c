#!/usr/bin/env python3
"""
Test Professional Strategy
Kiểm tra chiến l<PERSON><PERSON><PERSON> chuyên nghiệp với mock data
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import ConfigManager
from src.strategy.strategy_integration import StrategyIntegration
from src.strategy.gold_strategy import MarketState
from src.indicators.technical_indicators import TechnicalIndicators, MACDSignal, ATRData, PivotPoints, RSIData, BollingerBands, MarketRegime
from src.core.mt5_client import PositionInfo
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class ProfessionalStrategyTest:
    """Test Professional Strategy với mock data"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load_config()
        
        # Apply professional settings
        self.config.risk.max_risk_per_trade = 0.01
        self.config.risk.max_drawdown = 0.15
        self.config.trading.max_positions = 2
        
        self.strategy = StrategyIntegration(self.config)
        self.indicators = TechnicalIndicators()
        
        # Test results
        self.signals_generated = 0
        self.signals_executed = 0
        self.test_results = []
        
    def create_mock_market_state(self, price: float = 2000.0) -> MarketState:
        """Create mock market state for testing"""
        
        # Create mock MACD signal
        macd_signal = MACDSignal(
            macd=0.5,
            signal=0.3,
            histogram=0.2,
            crossover='bullish_cross',
            trend='bullish'
        )
        
        # Create mock ATR data
        atr_data = ATRData(
            atr=20.0,
            atr_percentage=0.01,
            volatility_level='medium'
        )
        
        # Create mock pivot points
        pivot_points = PivotPoints(
            pivot=price,
            r1=price + 10,
            r2=price + 20,
            r3=price + 30,
            s1=price - 10,
            s2=price - 20,
            s3=price - 30,
            current_level='above_pivot'
        )
        
        # Create mock RSI data
        rsi_data = RSIData(
            rsi=55.0,
            signal='neutral'
        )
        
        # Create mock Bollinger Bands
        bb_data = BollingerBands(
            upper_band=price + 25,
            middle_band=price,
            lower_band=price - 25,
            bandwidth=0.025,
            position='middle'
        )
        
        # Create mock market regime
        market_regime = MarketRegime(
            trend_direction='bullish',
            trend_strength=0.7,
            market_phase='trending',
            volatility_regime='normal'
        )
        
        # Create market state
        market_state = MarketState(
            price=price,
            bid=price - 0.5,
            ask=price + 0.5,
            spread=1.0,
            macd_signal=macd_signal,
            atr_data=atr_data,
            pivot_points=pivot_points,
            rsi_data=rsi_data,
            bollinger_bands=bb_data,
            market_regime=market_regime,
            signal_confirmation={'buy_confirmations': 4, 'sell_confirmations': 1},
            ai_prediction=1,
            ai_confidence=0.8,
            market_trend='bullish',
            volatility='medium'
        )
        
        return market_state
    
    def create_mock_account_info(self, balance: float = 1000.0) -> dict:
        """Create mock account info"""
        return {
            'balance': balance,
            'equity': balance,
            'margin_level': 500,
            'leverage': 100
        }
    
    def test_signal_generation(self):
        """Test signal generation với different market conditions"""
        print("🧪 Testing Signal Generation")
        print("=" * 50)
        
        test_scenarios = [
            {
                'name': 'Strong Bullish',
                'price': 2000.0,
                'macd_trend': 'bullish',
                'macd_crossover': 'bullish_cross',
                'rsi': 55.0,
                'expected': 'buy'
            },
            {
                'name': 'Strong Bearish', 
                'price': 2000.0,
                'macd_trend': 'bearish',
                'macd_crossover': 'bearish_cross',
                'rsi': 45.0,
                'expected': 'sell'
            },
            {
                'name': 'Neutral Market',
                'price': 2000.0,
                'macd_trend': 'neutral',
                'macd_crossover': 'none',
                'rsi': 50.0,
                'expected': None
            },
            {
                'name': 'High Volatility',
                'price': 2000.0,
                'macd_trend': 'bullish',
                'macd_crossover': 'bullish_cross',
                'rsi': 55.0,
                'volatility': 'high',
                'expected': None  # Should be rejected due to high volatility
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 Testing: {scenario['name']}")
            
            # Create market state for scenario
            market_state = self.create_mock_market_state(scenario['price'])
            
            # Modify based on scenario
            market_state.macd_signal.trend = scenario['macd_trend']
            market_state.macd_signal.crossover = scenario['macd_crossover']
            market_state.rsi_data.rsi = scenario['rsi']
            
            if 'volatility' in scenario:
                market_state.atr_data.volatility_level = scenario['volatility']
                market_state.volatility = scenario['volatility']
            
            # Test signal generation
            account_info = self.create_mock_account_info()
            positions = []  # No existing positions
            
            signal = self.strategy.generate_trading_signal(
                market_state, positions, account_info
            )
            
            self.signals_generated += 1
            
            if signal:
                self.signals_executed += 1
                print(f"✅ Signal Generated: {signal.action}")
                print(f"   Entry Price: {signal.entry_price:.2f}")
                print(f"   Stop Loss: {signal.stop_loss:.2f}")
                print(f"   Take Profit: {signal.take_profit:.2f}")
                print(f"   Volume: {signal.volume:.2f}")
                print(f"   Confidence: {signal.confidence:.2%}")
                print(f"   Reasoning: {signal.reasoning[:100]}...")
                
                # Validate signal
                if scenario['expected'] and signal.action == scenario['expected']:
                    print(f"✅ Expected signal type: {scenario['expected']}")
                elif scenario['expected']:
                    print(f"⚠️ Expected {scenario['expected']}, got {signal.action}")
                
                self.test_results.append({
                    'scenario': scenario['name'],
                    'expected': scenario['expected'],
                    'actual': signal.action,
                    'success': signal.action == scenario['expected']
                })
            else:
                print("❌ No Signal Generated")
                if scenario['expected'] is None:
                    print("✅ Correctly rejected signal")
                    self.test_results.append({
                        'scenario': scenario['name'],
                        'expected': scenario['expected'],
                        'actual': None,
                        'success': True
                    })
                else:
                    print(f"⚠️ Expected {scenario['expected']}, got None")
                    self.test_results.append({
                        'scenario': scenario['name'],
                        'expected': scenario['expected'],
                        'actual': None,
                        'success': False
                    })
    
    def test_risk_management(self):
        """Test risk management features"""
        print(f"\n🛡️ Testing Risk Management")
        print("=" * 50)
        
        # Test with different account balances
        balances = [100, 1000, 10000]
        
        for balance in balances:
            print(f"\n💰 Testing with balance: ${balance}")
            
            market_state = self.create_mock_market_state()
            account_info = self.create_mock_account_info(balance)
            positions = []
            
            signal = self.strategy.generate_trading_signal(
                market_state, positions, account_info
            )
            
            if signal:
                risk_amount = abs(signal.entry_price - signal.stop_loss) * signal.volume
                risk_percentage = risk_amount / balance
                
                print(f"   Risk Amount: ${risk_amount:.2f}")
                print(f"   Risk Percentage: {risk_percentage:.2%}")
                print(f"   Volume: {signal.volume:.2f}")
                
                # Validate risk limits
                if risk_percentage <= 0.015:  # 1.5% max (with some tolerance)
                    print("✅ Risk within limits")
                else:
                    print("⚠️ Risk exceeds limits")
    
    def test_position_management(self):
        """Test position management"""
        print(f"\n📈 Testing Position Management")
        print("=" * 50)
        
        # Create existing position
        existing_position = PositionInfo(
            ticket=12345,
            symbol="XAUUSD",
            type=0,  # Buy position
            volume=0.1,
            price_open=2000.0,
            price_current=2020.0,  # In profit
            profit=20.0,
            swap=0.0,
            commission=0.0,
            time=datetime.now() - timedelta(hours=2)
        )
        
        market_state = self.create_mock_market_state(2020.0)  # Current price
        account_info = self.create_mock_account_info()
        positions = [existing_position]
        
        # Test exit signal generation
        signal = self.strategy.generate_trading_signal(
            market_state, positions, account_info
        )
        
        if signal and signal.action in ['close', 'partial_close']:
            print(f"✅ Exit signal generated: {signal.action}")
            print(f"   Reason: {signal.reasoning}")
        else:
            print("ℹ️ Position maintained")
    
    def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        print("🚀 Professional Strategy Comprehensive Test")
        print("=" * 70)
        
        try:
            # Test signal generation
            self.test_signal_generation()
            
            # Test risk management
            self.test_risk_management()
            
            # Test position management
            self.test_position_management()
            
            # Print summary
            self.print_test_summary()
            
        except Exception as e:
            logger.error(f"Error in comprehensive test: {e}")
            print(f"❌ Test failed: {e}")
    
    def print_test_summary(self):
        """Print test summary"""
        print(f"\n📊 TEST SUMMARY")
        print("=" * 50)
        
        print(f"Total Signals Generated: {self.signals_generated}")
        print(f"Signals Executed: {self.signals_executed}")
        print(f"Execution Rate: {(self.signals_executed/self.signals_generated*100):.1f}%" if self.signals_generated > 0 else "0%")
        
        # Test results
        if self.test_results:
            successful_tests = sum(1 for r in self.test_results if r['success'])
            total_tests = len(self.test_results)
            success_rate = successful_tests / total_tests * 100
            
            print(f"\nTest Results:")
            print(f"Successful Tests: {successful_tests}/{total_tests}")
            print(f"Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("✅ EXCELLENT - Strategy working as expected!")
            elif success_rate >= 60:
                print("✅ GOOD - Minor issues to address")
            else:
                print("⚠️ NEEDS IMPROVEMENT - Major issues detected")
        
        # Get strategy status
        try:
            status = self.strategy.get_strategy_status()
            print(f"\n🔧 Strategy Status:")
            print(f"Engine: {status.get('strategy_engine', 'Unknown')}")
            print(f"Status: {status.get('status', 'Unknown')}")
        except Exception as e:
            print(f"⚠️ Could not get strategy status: {e}")

def main():
    """Main function"""
    try:
        test = ProfessionalStrategyTest()
        test.run_comprehensive_test()
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
