#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng tạo biểu đồ PNG
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Import các class cần thiết
from src.backtesting.backtest_engine import BacktestResults, BacktestReporter, Trade

def create_sample_backtest_results():
    """Tạo dữ liệu backtest mẫu để test"""

    # Tạo dữ liệu equity curve
    dates = pd.date_range(start='2025-06-01', end='2025-07-01', freq='H')
    initial_balance = 10000

    # Tạo equity curve với random walk
    returns = np.random.normal(0.0001, 0.01, len(dates))
    equity_values = [initial_balance]

    for ret in returns[1:]:
        new_value = equity_values[-1] * (1 + ret)
        equity_values.append(new_value)

    equity_curve = pd.Series(equity_values, index=dates)

    # Tạo drawdown curve
    peak = equity_curve.expanding().max()
    drawdown_curve = (equity_curve - peak) / peak

    # Tạo sample trades
    trades = []
    for i in range(50):
        entry_time = dates[i * 10]
        exit_time = entry_time + timedelta(hours=np.random.randint(1, 24))

        direction = np.random.choice(['buy', 'sell'])
        volume = round(np.random.uniform(0.01, 0.1), 2)
        entry_price = 2000 + np.random.normal(0, 10)
        exit_price = entry_price + np.random.normal(0, 5)

        pnl = (exit_price - entry_price) * volume * 100 if direction == 'buy' else (entry_price - exit_price) * volume * 100

        trade = Trade(
            entry_time=entry_time,
            exit_time=exit_time,
            symbol="XAUUSD",
            direction=direction,
            entry_price=entry_price,
            exit_price=exit_price,
            volume=volume,
            pnl=pnl,
            pnl_pct=pnl / (entry_price * volume * 100) * 100,
            commission=3.0,
            swap=0.0,
            duration_minutes=int((exit_time - entry_time).total_seconds() / 60),
            entry_reason="Test signal",
            exit_reason="Test exit",
            stop_loss_price=entry_price - 10 if direction == 'buy' else entry_price + 10,
            take_profit_price=entry_price + 15 if direction == 'buy' else entry_price - 15,
            max_favorable_excursion=abs(pnl) * 1.2,
            max_adverse_excursion=abs(pnl) * 0.8
        )
        trades.append(trade)

    # Tính toán metrics
    winning_trades = sum(1 for t in trades if t.pnl > 0)
    losing_trades = len(trades) - winning_trades
    win_rate = winning_trades / len(trades) if trades else 0

    total_pnl = sum(t.pnl for t in trades)
    gross_profit = sum(t.pnl for t in trades if t.pnl > 0)
    gross_loss = abs(sum(t.pnl for t in trades if t.pnl < 0))
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

    max_drawdown_pct = abs(drawdown_curve.min())

    # Tạo BacktestResults
    results = BacktestResults(
        start_date=dates[0],
        end_date=dates[-1],
        initial_balance=initial_balance,
        final_balance=equity_curve.iloc[-1],
        total_return=equity_curve.iloc[-1] - initial_balance,
        total_return_pct=(equity_curve.iloc[-1] - initial_balance) / initial_balance,
        max_drawdown_pct=max_drawdown_pct,
        sharpe_ratio=np.random.uniform(0.5, 2.0),
        sortino_ratio=np.random.uniform(0.6, 2.2),
        calmar_ratio=np.random.uniform(0.3, 1.5),
        total_trades=len(trades),
        winning_trades=winning_trades,
        losing_trades=losing_trades,
        win_rate=win_rate,
        profit_factor=profit_factor,
        avg_trade_duration=np.mean([t.duration_minutes for t in trades]),
        max_consecutive_wins=np.random.randint(3, 8),
        max_consecutive_losses=np.random.randint(2, 6),
        trades=trades,
        equity_curve=equity_curve,
        drawdown_curve=drawdown_curve
    )

    return results

def test_png_generation():
    """Test tạo biểu đồ PNG bằng cách chạy backtest thực"""
    print("🧪 Testing PNG chart generation with real backtest...")

    try:
        # Import các module cần thiết
        from src.utils.config import ConfigManager
        from src.backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig

        # Load configuration
        print("📝 Loading configuration...")
        config_manager = ConfigManager()
        config = config_manager.config

        # Tạo backtest engine
        print("🔧 Creating backtest engine...")
        engine = BacktestEngine(config)

        # Tạo simple backtest config
        simple_config = SimpleBacktestConfig(
            period="1week",
            initial_balance=10000
        )

        # Chạy backtest
        print("🚀 Running backtest...")
        results = engine.run_simple_backtest(simple_config)

        if results:
            print(f"✅ Backtest completed: {results.total_trades} trades")

            # Tạo báo cáo với PNG charts
            print("📊 Generating report with PNG charts...")
            report_path = engine.generate_report(results)

            # Kiểm tra files PNG đã được tạo
            reports_dir = Path("reports")
            png_files = list(reports_dir.glob("*.png"))

            print(f"\n📊 Found {len(png_files)} PNG files:")
            for png_file in png_files:
                size_kb = png_file.stat().st_size / 1024
                print(f"   ✅ {png_file.name} ({size_kb:.1f} KB)")

            if Path(report_path).exists():
                size_kb = Path(report_path).stat().st_size / 1024
                print(f"\n📄 HTML report: {report_path} ({size_kb:.1f} KB)")

            print(f"\n🎯 Test completed! Check the 'reports/' directory for PNG files.")
        else:
            print("❌ Backtest failed to produce results")

    except Exception as e:
        print(f"❌ Error during PNG generation test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_png_generation()
