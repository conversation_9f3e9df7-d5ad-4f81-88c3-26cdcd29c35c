#!/usr/bin/env python3
"""
Simple test script để kiểm tra tính năng tạo biểu đồ PNG
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from pathlib import Path

def test_simple_png_creation():
    """Test tạo biểu đồ PNG đơn giản"""
    print("🧪 Testing simple PNG chart creation...")
    
    try:
        # Tạo thư mục reports
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        # Test 1: Equity Curve
        print("📊 Creating equity curve chart...")
        dates = pd.date_range(start='2025-06-01', end='2025-07-01', freq='h')
        initial_balance = 10000
        
        # Tạo equity curve với random walk
        returns = np.random.normal(0.0001, 0.01, len(dates))
        equity_values = [initial_balance]
        
        for ret in returns[1:]:
            new_value = equity_values[-1] * (1 + ret)
            equity_values.append(new_value)
        
        equity_curve = pd.Series(equity_values, index=dates)
        
        # Tạo biểu đồ equity curve
        plt.figure(figsize=(12, 6))
        plt.plot(equity_curve.index, equity_curve.values, linewidth=2, color='blue')
        plt.title('Equity Curve', fontsize=16, fontweight='bold')
        plt.xlabel('Date')
        plt.ylabel('Balance ($)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        equity_path = reports_dir / "equity_curve.png"
        plt.savefig(equity_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        if equity_path.exists():
            size_kb = equity_path.stat().st_size / 1024
            print(f"   ✅ equity_curve.png ({size_kb:.1f} KB)")
        
        # Test 2: Drawdown Chart
        print("📊 Creating drawdown chart...")
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak * 100
        
        plt.figure(figsize=(12, 6))
        plt.fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
        plt.plot(drawdown.index, drawdown.values, linewidth=2, color='red')
        plt.title('Drawdown', fontsize=16, fontweight='bold')
        plt.xlabel('Date')
        plt.ylabel('Drawdown (%)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        drawdown_path = reports_dir / "drawdown.png"
        plt.savefig(drawdown_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        if drawdown_path.exists():
            size_kb = drawdown_path.stat().st_size / 1024
            print(f"   ✅ drawdown.png ({size_kb:.1f} KB)")
        
        # Test 3: Monthly Returns Heatmap
        print("📊 Creating monthly returns heatmap...")
        monthly_returns = equity_curve.resample('M').last().pct_change().dropna() * 100
        
        # Tạo dữ liệu cho heatmap
        years = monthly_returns.index.year.unique()
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        heatmap_data = pd.DataFrame(index=years, columns=months)
        
        for date, ret in monthly_returns.items():
            year = date.year
            month = months[date.month - 1]
            heatmap_data.loc[year, month] = ret
        
        plt.figure(figsize=(12, 6))
        sns.heatmap(heatmap_data.astype(float), annot=True, fmt='.1f', 
                   cmap='RdYlGn', center=0, cbar_kws={'label': 'Return (%)'})
        plt.title('Monthly Returns Heatmap', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        heatmap_path = reports_dir / "monthly_returns.png"
        plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        if heatmap_path.exists():
            size_kb = heatmap_path.stat().st_size / 1024
            print(f"   ✅ monthly_returns.png ({size_kb:.1f} KB)")
        
        # Test 4: Trade Analysis
        print("📊 Creating trade analysis chart...")
        
        # Tạo dữ liệu trades giả
        n_trades = 50
        trade_pnl = np.random.normal(10, 50, n_trades)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # P&L Distribution
        ax1.hist(trade_pnl, bins=20, alpha=0.7, color='blue', edgecolor='black')
        ax1.set_title('P&L Distribution')
        ax1.set_xlabel('P&L ($)')
        ax1.set_ylabel('Frequency')
        ax1.grid(True, alpha=0.3)
        
        # Cumulative P&L
        cumulative_pnl = np.cumsum(trade_pnl)
        ax2.plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2, color='green')
        ax2.set_title('Cumulative P&L')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Cumulative P&L ($)')
        ax2.grid(True, alpha=0.3)
        
        # Win/Loss Ratio
        wins = sum(1 for pnl in trade_pnl if pnl > 0)
        losses = len(trade_pnl) - wins
        ax3.pie([wins, losses], labels=['Wins', 'Losses'], autopct='%1.1f%%', 
               colors=['green', 'red'])
        ax3.set_title('Win/Loss Ratio')
        
        # Trade Duration (giả)
        durations = np.random.exponential(120, n_trades)  # minutes
        ax4.hist(durations, bins=20, alpha=0.7, color='orange', edgecolor='black')
        ax4.set_title('Trade Duration Distribution')
        ax4.set_xlabel('Duration (minutes)')
        ax4.set_ylabel('Frequency')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        trade_analysis_path = reports_dir / "trade_analysis.png"
        plt.savefig(trade_analysis_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        if trade_analysis_path.exists():
            size_kb = trade_analysis_path.stat().st_size / 1024
            print(f"   ✅ trade_analysis.png ({size_kb:.1f} KB)")
        
        # Kiểm tra tất cả files PNG
        png_files = list(reports_dir.glob("*.png"))
        print(f"\n🎯 Successfully created {len(png_files)} PNG charts!")
        
        total_size = sum(f.stat().st_size for f in png_files) / 1024
        print(f"📊 Total size: {total_size:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during PNG creation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_png_creation()
    if success:
        print("\n✅ PNG chart generation test PASSED!")
    else:
        print("\n❌ PNG chart generation test FAILED!")
