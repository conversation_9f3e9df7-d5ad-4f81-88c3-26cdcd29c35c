#!/usr/bin/env python3
"""
Professional Strategy Backtest
Chạy backtest với chiến lư<PERSON><PERSON> chuyên nghiệp mới để kiểm tra hiệu suất
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import ConfigManager
from src.backtesting.backtest_engine import BacktestEngine
from src.strategy.strategy_integration import StrategyIntegration
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class ProfessionalBacktest:
    """Professional Strategy Backtest Runner"""

    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load_config()

        # Override config for professional settings
        self._apply_professional_config()

        self.backtest_engine = BacktestEngine(self.config)

    def _apply_professional_config(self):
        """Apply professional trading configuration"""
        # Professional risk settings
        self.config.risk.max_risk_per_trade = 0.01      # 1% per trade
        self.config.risk.max_daily_risk = 0.03          # 3% daily max
        self.config.risk.max_drawdown = 0.15            # 15% max drawdown
        self.config.risk.min_risk_reward_ratio = 2.0    # 1:2 minimum R:R

        # Professional trading settings
        self.config.trading.max_positions = 2           # Max 2 positions
        self.config.trading.max_daily_trades = 5        # Max 5 trades per day
        self.config.trading.default_volume = 0.01       # Conservative volume

        # Professional strategy settings
        self.config.strategy_scoring.min_signal_strength = 70  # Higher threshold
        self.config.strategy_scoring.min_ai_confidence = 0.8   # Higher confidence

        logger.info("Professional configuration applied")

    def run_comprehensive_backtest(self, start_date: str = None, end_date: str = None):
        """Run comprehensive backtest with multiple scenarios"""

        # Default to last 3 months if no dates provided
        if not start_date or not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

        logger.info(f"🚀 Starting Professional Strategy Backtest")
        logger.info(f"📅 Period: {start_date} to {end_date}")
        logger.info(f"💰 Initial Balance: ${self.config.backtest.initial_balance:,.2f}")

        # Test scenarios
        scenarios = [
            {
                'name': 'Conservative',
                'risk_per_trade': 0.005,  # 0.5%
                'min_confirmations': 5,
                'min_signal_strength': 0.8
            },
            {
                'name': 'Balanced',
                'risk_per_trade': 0.01,   # 1%
                'min_confirmations': 4,
                'min_signal_strength': 0.7
            },
            {
                'name': 'Aggressive',
                'risk_per_trade': 0.015,  # 1.5%
                'min_confirmations': 3,
                'min_signal_strength': 0.6
            }
        ]

        results = {}

        for scenario in scenarios:
            logger.info(f"\n📊 Testing {scenario['name']} Scenario")

            # Apply scenario settings
            self.config.risk.max_risk_per_trade = scenario['risk_per_trade']

            # Get historical data first
            from src.core.mt5_client_mock import MockMT5Client
            mock_client = MockMT5Client(self.config)

            # Generate mock data for the period
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # Create simple mock data
            import pandas as pd
            import numpy as np

            # Generate date range
            date_range = pd.date_range(start=start_dt, end=end_dt, freq='5min')

            # Generate mock OHLC data
            np.random.seed(42)  # For reproducible results
            base_price = 2000.0
            data = []

            for i, timestamp in enumerate(date_range):
                # Simple random walk
                change = np.random.normal(0, 0.001) * base_price
                base_price += change

                high = base_price + abs(np.random.normal(0, 0.0005)) * base_price
                low = base_price - abs(np.random.normal(0, 0.0005)) * base_price
                close = base_price + np.random.normal(0, 0.0002) * base_price
                volume = np.random.randint(100, 1000)

                data.append({
                    'time': timestamp,
                    'open': base_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'tick_volume': volume
                })

            mock_data = pd.DataFrame(data)

            # Run backtest with mock data
            result = self.backtest_engine.run_backtest(
                data=mock_data,
                start_date=start_dt,
                end_date=end_dt
            )

            if result:
                results[scenario['name']] = result
                self._print_scenario_summary(scenario['name'], result)
            else:
                logger.error(f"❌ {scenario['name']} scenario failed")

        # Generate comprehensive report
        if results:
            self._generate_comprehensive_report(results, start_date, end_date)
            self._create_comparison_charts(results)

        return results

    def _print_scenario_summary(self, scenario_name: str, result):
        """Print summary for a scenario"""
        print(f"\n{'='*50}")
        print(f"📈 {scenario_name} Scenario Results")
        print(f"{'='*50}")
        print(f"💰 Final Balance: ${result.final_balance:,.2f}")
        print(f"📊 Total Return: {result.total_return_pct:.2%}")
        print(f"📉 Max Drawdown: {result.max_drawdown_pct:.2%}")
        print(f"🎯 Win Rate: {result.win_rate:.1%}")
        print(f"⚡ Profit Factor: {result.profit_factor:.2f}")
        print(f"📈 Sharpe Ratio: {result.sharpe_ratio:.2f}")
        print(f"🔢 Total Trades: {result.total_trades}")
        print(f"✅ Winning Trades: {result.winning_trades}")
        print(f"❌ Losing Trades: {result.losing_trades}")
        print(f"💵 Avg Win: ${result.avg_win:.2f}")
        print(f"💸 Avg Loss: ${result.avg_loss:.2f}")

        # Performance assessment
        self._assess_performance(result)

    def _assess_performance(self, result):
        """Assess performance quality"""
        print(f"\n🎯 Performance Assessment:")

        # Overall score
        score = 0

        # Return component
        if result.total_return_pct > 0.20:  # >20% return
            score += 25
            print("✅ Excellent returns (>20%)")
        elif result.total_return_pct > 0.10:  # >10% return
            score += 15
            print("✅ Good returns (>10%)")
        elif result.total_return_pct > 0.05:  # >5% return
            score += 10
            print("⚠️ Moderate returns (>5%)")
        else:
            print("❌ Poor returns (<5%)")

        # Drawdown component
        if result.max_drawdown_pct < 0.10:  # <10% drawdown
            score += 25
            print("✅ Excellent risk control (<10% drawdown)")
        elif result.max_drawdown_pct < 0.15:  # <15% drawdown
            score += 15
            print("✅ Good risk control (<15% drawdown)")
        elif result.max_drawdown_pct < 0.20:  # <20% drawdown
            score += 10
            print("⚠️ Moderate risk control (<20% drawdown)")
        else:
            print("❌ Poor risk control (>20% drawdown)")

        # Win rate component
        if result.win_rate > 0.60:  # >60% win rate
            score += 25
            print("✅ Excellent win rate (>60%)")
        elif result.win_rate > 0.50:  # >50% win rate
            score += 15
            print("✅ Good win rate (>50%)")
        elif result.win_rate > 0.40:  # >40% win rate
            score += 10
            print("⚠️ Moderate win rate (>40%)")
        else:
            print("❌ Poor win rate (<40%)")

        # Profit factor component
        if result.profit_factor > 1.5:  # >1.5 profit factor
            score += 25
            print("✅ Excellent profit factor (>1.5)")
        elif result.profit_factor > 1.2:  # >1.2 profit factor
            score += 15
            print("✅ Good profit factor (>1.2)")
        elif result.profit_factor > 1.0:  # >1.0 profit factor
            score += 10
            print("⚠️ Moderate profit factor (>1.0)")
        else:
            print("❌ Poor profit factor (<1.0)")

        # Overall assessment
        print(f"\n🏆 Overall Score: {score}/100")
        if score >= 80:
            print("🌟 EXCELLENT STRATEGY - Ready for live trading!")
        elif score >= 60:
            print("✅ GOOD STRATEGY - Consider minor optimizations")
        elif score >= 40:
            print("⚠️ MODERATE STRATEGY - Needs improvement")
        else:
            print("❌ POOR STRATEGY - Major revisions needed")

    def _generate_comprehensive_report(self, results: dict, start_date: str, end_date: str):
        """Generate comprehensive comparison report"""
        print(f"\n{'='*70}")
        print(f"📊 COMPREHENSIVE PROFESSIONAL STRATEGY REPORT")
        print(f"{'='*70}")
        print(f"📅 Period: {start_date} to {end_date}")
        print(f"💰 Initial Balance: ${self.config.backtest.initial_balance:,.2f}")

        # Comparison table
        print(f"\n📈 SCENARIO COMPARISON")
        print(f"{'Scenario':<12} {'Return':<8} {'Drawdown':<10} {'Win Rate':<9} {'Profit Factor':<13} {'Trades':<7}")
        print("-" * 70)

        best_scenario = None
        best_score = 0

        for name, result in results.items():
            # Calculate composite score
            score = (
                (result.total_return_pct * 100) +
                (max(0, 20 - result.max_drawdown_pct * 100)) +
                (result.win_rate * 50) +
                (min(result.profit_factor, 3) * 20)
            )

            if score > best_score:
                best_score = score
                best_scenario = name

            print(f"{name:<12} {result.total_return_pct:>7.1%} {result.max_drawdown_pct:>9.1%} "
                  f"{result.win_rate:>8.1%} {result.profit_factor:>12.2f} {result.total_trades:>6}")

        print(f"\n🏆 BEST SCENARIO: {best_scenario}")

        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        best_result = results[best_scenario]

        if best_result.total_return_pct > 0.15 and best_result.max_drawdown_pct < 0.15:
            print("✅ Strategy is ready for live trading with current parameters")
        elif best_result.total_return_pct > 0.10:
            print("⚠️ Strategy shows promise but consider reducing risk further")
        else:
            print("❌ Strategy needs significant improvement before live trading")

        # Risk assessment
        print(f"\n⚠️ RISK ASSESSMENT:")
        if best_result.max_drawdown_pct < 0.10:
            print("✅ Low risk - Drawdown well controlled")
        elif best_result.max_drawdown_pct < 0.20:
            print("⚠️ Moderate risk - Monitor drawdown closely")
        else:
            print("❌ High risk - Reduce position sizes")

    def _create_comparison_charts(self, results: dict):
        """Create comparison charts"""
        try:
            # Set up the plotting style
            plt.style.use('seaborn-v0_8')
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

            scenarios = list(results.keys())
            returns = [results[s].total_return_pct * 100 for s in scenarios]
            drawdowns = [results[s].max_drawdown_pct * 100 for s in scenarios]
            win_rates = [results[s].win_rate * 100 for s in scenarios]
            profit_factors = [results[s].profit_factor for s in scenarios]

            # Returns comparison
            bars1 = ax1.bar(scenarios, returns, color=['green' if r > 0 else 'red' for r in returns])
            ax1.set_title('Total Returns by Scenario', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Return (%)')
            ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            for bar, value in zip(bars1, returns):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        f'{value:.1f}%', ha='center', va='bottom')

            # Drawdown comparison
            bars2 = ax2.bar(scenarios, drawdowns, color='red', alpha=0.7)
            ax2.set_title('Maximum Drawdown by Scenario', fontsize=14, fontweight='bold')
            ax2.set_ylabel('Drawdown (%)')
            ax2.axhline(y=15, color='orange', linestyle='--', alpha=0.7, label='15% Limit')
            ax2.legend()
            for bar, value in zip(bars2, drawdowns):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                        f'{value:.1f}%', ha='center', va='bottom')

            # Win rate comparison
            bars3 = ax3.bar(scenarios, win_rates, color='blue', alpha=0.7)
            ax3.set_title('Win Rate by Scenario', fontsize=14, fontweight='bold')
            ax3.set_ylabel('Win Rate (%)')
            ax3.axhline(y=50, color='orange', linestyle='--', alpha=0.7, label='50% Benchmark')
            ax3.legend()
            for bar, value in zip(bars3, win_rates):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        f'{value:.1f}%', ha='center', va='bottom')

            # Profit factor comparison
            bars4 = ax4.bar(scenarios, profit_factors, color='purple', alpha=0.7)
            ax4.set_title('Profit Factor by Scenario', fontsize=14, fontweight='bold')
            ax4.set_ylabel('Profit Factor')
            ax4.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Break-even')
            ax4.axhline(y=1.5, color='green', linestyle='--', alpha=0.7, label='Good')
            ax4.legend()
            for bar, value in zip(bars4, profit_factors):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                        f'{value:.2f}', ha='center', va='bottom')

            plt.tight_layout()

            # Save chart
            chart_filename = f"professional_strategy_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            print(f"\n📊 Comparison chart saved: {chart_filename}")

            plt.show()

        except Exception as e:
            logger.error(f"Error creating comparison charts: {e}")

def main():
    """Main function"""
    print("🚀 Professional Strategy Backtest")
    print("=" * 50)

    try:
        backtest = ProfessionalBacktest()

        # Run comprehensive backtest
        results = backtest.run_comprehensive_backtest()

        if results:
            print(f"\n✅ Backtest completed successfully!")
            print(f"📊 {len(results)} scenarios tested")
        else:
            print(f"\n❌ Backtest failed!")

    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
