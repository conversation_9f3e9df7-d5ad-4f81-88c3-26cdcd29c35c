ai:
  batch_size: 32
  epsilon_decay: 0.995
  epsilon_end: 0.01
  epsilon_start: 1.0
  learning_rate: 0.001
  memory_size: 10000
  model_type: dqn
  target_update_frequency: 100
  training_frequency: 4
backtest:
  # Thời gian backtest - <PERSON><PERSON> dàng thay đổi để test cá<PERSON> khoảng thời gian khác nhau
  start_date: '2025-01-01'    # <PERSON><PERSON><PERSON> bắt đầu backtest (YYYY-MM-DD)
  end_date: '2025-07-11'      # <PERSON><PERSON><PERSON> kết thúc backtest (YYYY-MM-DD)

  # C<PERSON>u hình tài chính
  initial_balance: 200        # Số dư ban đầu
  commission: 0.03            # Phí giao dịch
  spread: 200                 # Spread trung bình (points)

  # C<PERSON>u hình khác
  logging_level: ERROR        # Chỉ hiện lỗi nghiêm trọng, giảm spam log
enable_ai: true
enable_backtesting: false
indicators:
  atr_period: 21
  macd_fast: 8
  macd_signal: 5
  macd_slow: 21
  pivot_method: standard
live_trading: true
logging_level: INFO
mt5:
  login: 126780412
  magic_number: 234000
  password: 'Exnessdz123@199'
  server: 'Exness-MT5Real7'
  symbol: XAUUSD
risk:
  # Professional Risk Management Settings
  max_drawdown: 0.15              # 15% maximum drawdown (professional level)
  max_spread: 150                 # Tighter spread control
  stop_loss_atr_multiplier: 1.5   # Tighter stop loss for better R:R
  take_profit_atr_multiplier: 3.0 # Maintain 1:2 risk/reward minimum
  trailing_stop: true
  trailing_stop_distance: 30      # Tighter trailing stop
  margin_level_min: 300           # Higher margin requirement
  max_total_risk_exposure: 0.05   # Maximum 5% total risk exposure (conservative)
  max_risk_per_trade: 0.01        # 1% risk per trade (professional standard)
  max_portfolio_risk: 0.03        # 3% maximum portfolio risk
  max_positions: 2                # Maximum 2 positions (focus & control)
  trailing_stop_enabled: true
  kelly_fraction: 0.25            # Conservative Kelly fraction
  max_consecutive_losses: 5       # Stop after 5 consecutive losses
  daily_loss_limit: 0.03          # 3% daily loss limit
strategy:
  # Professional Strategy Scoring (Conservative & Selective)
  scoring:
    macd_crossover_bullish: 0.3     # Reduced weight for more selectivity
    macd_crossover_bearish: 0.3
    macd_trend_bullish: 0.15
    macd_trend_bearish: 0.15
    pivot_above: 0.15
    pivot_below: 0.15
    market_trend_bullish: 0.2
    market_trend_bearish: 0.2
    ai_prediction_weight: 0.3       # Reduced AI weight for stability
    technical_weight: 1.0
    ai_weight: 0.0                  # Disable AI for now (focus on technical)
    min_signal_strength: 0.7        # Higher threshold for quality signals
    min_ai_confidence: 0.8          # Higher AI confidence requirement

trading:
  # Professional Trading Settings
  max_daily_trades: 5             # Reduced for quality over quantity
  max_positions: 2                # Maximum 2 positions for better control
  max_volume: 0.5                 # Reduced maximum volume for safety
  min_volume: 0.01
  risk_per_trade: 0.01            # 1% risk per trade (professional standard)
  timeframe: 5                    # 5-minute timeframe
  volume_step: 0.01
  default_volume: 0.01            # Conservative default volume

# Enhanced features configuration
strategy_scoring:
  macd_crossover_bullish: 25
  macd_crossover_bearish: 25
  rsi_oversold_bullish: 15
  rsi_overbought_bearish: 15
  bb_squeeze_breakout: 20
  pivot_support_resistance: 10
  min_signal_strength: 60
  min_ai_confidence: 0.7

adaptive_strategy:
  enabled: true
  trending_multiplier: 1.5
  ranging_multiplier: 0.8
  volatility_adjustment: true
  time_filters:
    avoid_news_events: true
    london_session_boost: 1.2
    asian_session_reduce: 0.7
    friday_close_early: true

performance:
  update_frequency: "1min"
  save_frequency: "5min"
  alert_thresholds:
    max_drawdown: 0.15
    consecutive_losses: 5
    low_win_rate: 0.4
  dashboard_refresh: "30sec"
