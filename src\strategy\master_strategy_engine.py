"""
Master Strategy Engine
Tích hợp tất cả các components để tạo ra một trading system chuyên nghiệp
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from .professional_strategy import ProfessionalStrategy, ProfessionalSignal
from .enhanced_entry_exit import EnhancedEntryExit, ExitSignal
from ..risk.professional_risk_manager import ProfessionalRiskManager, PositionRisk
from ..indicators.technical_indicators import TechnicalIndicators
from ..utils.logger import setup_logger
from ..strategy.gold_strategy import TradingSignal, MarketState
from ..core.mt5_client import PositionInfo

logger = setup_logger(__name__)

@dataclass
class MasterSignal:
    """Master trading signal với tất cả thông tin cần thiết"""
    action: str  # 'buy', 'sell', 'close', 'partial_close'
    strength: float
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    volume: float
    risk_amount: float
    risk_percentage: float
    risk_reward_ratio: float
    confirmations: List[str]
    market_regime: str
    exit_strategy: str
    reasoning: str
    timestamp: datetime

    # Advanced features
    partial_exit: bool = False
    exit_percentage: float = 0.0
    trailing_stop_enabled: bool = True
    position_scaling: bool = False

class MasterStrategyEngine:
    """
    Master Strategy Engine tích hợp:
    1. Professional Strategy (Entry Logic)
    2. Enhanced Entry/Exit (Confirmation & Exit Logic)
    3. Professional Risk Manager (Risk Management)
    4. Advanced Position Management
    """

    def __init__(self, config, mt5_client=None):
        self.config = config
        self.mt5_client = mt5_client

        # Initialize components
        self.professional_strategy = ProfessionalStrategy(config, mt5_client)
        self.entry_exit_engine = EnhancedEntryExit(config, mt5_client)
        self.risk_manager = ProfessionalRiskManager(config)
        self.indicators = TechnicalIndicators()

        # Performance tracking
        self.total_signals_generated = 0
        self.signals_executed = 0
        self.signals_rejected = 0
        self.last_signal_time = None

        # Position tracking
        self.active_positions = {}
        self.position_history = []

    def generate_master_signal(self, market_state: MarketState,
                             current_positions: List[PositionInfo],
                             account_info: Dict) -> Optional[MasterSignal]:
        """
        Generate master trading signal với full analysis
        """
        try:
            self.total_signals_generated += 1

            # 1. Check existing positions first
            position_signals = self._check_existing_positions(current_positions, market_state, account_info)
            if position_signals:
                return position_signals[0]  # Return first position signal

            # 2. Generate new entry signal if no position management needed
            if len(current_positions) >= 2:  # Max 2 positions
                logger.info("Maximum positions reached, no new entries")
                return None

            # 3. Professional strategy analysis
            professional_signal = self.professional_strategy.generate_professional_signal(
                market_state, current_positions, account_info
            )

            if not professional_signal:
                return None

            # 4. Enhanced entry/exit confirmation
            entry_analysis = self.entry_exit_engine.analyze_entry_opportunity(market_state)

            if not entry_analysis:
                logger.info("Entry analysis failed - insufficient confirmations")
                return None

            # 5. Validate signal alignment
            if not self._validate_signal_alignment(professional_signal, entry_analysis):
                logger.info("Signal alignment validation failed")
                return None

            # 6. Risk management validation
            risk_data = {
                'entry_price': professional_signal.entry_price,
                'stop_loss': professional_signal.stop_loss,
                'take_profit': professional_signal.take_profit,
                'confidence': professional_signal.confidence
            }

            position_risk = self.risk_manager.calculate_position_size(
                risk_data, account_info, market_state.atr_data.atr / market_state.price
            )

            if not position_risk:
                logger.info("Risk management rejected the signal")
                self.signals_rejected += 1
                return None

            # 7. Create master signal
            master_signal = self._create_master_signal(
                professional_signal, entry_analysis, position_risk, market_state
            )

            if master_signal:
                self.signals_executed += 1
                self.last_signal_time = datetime.now()
                logger.info(f"Master signal generated: {master_signal.action} at {master_signal.entry_price}")

            return master_signal

        except Exception as e:
            logger.error(f"Error generating master signal: {e}")
            return None

    def _check_existing_positions(self, positions: List[PositionInfo],
                                market_state: MarketState,
                                account_info: Dict) -> List[MasterSignal]:
        """Check existing positions for exit signals"""
        exit_signals = []

        try:
            for position in positions:
                # Analyze exit opportunity
                exit_signal = self.entry_exit_engine.analyze_exit_opportunity(
                    position, market_state, account_info
                )

                if exit_signal.should_exit:
                    # Create exit master signal
                    master_exit = MasterSignal(
                        action='partial_close' if exit_signal.partial_exit else 'close',
                        strength=1.0,
                        confidence=1.0,
                        entry_price=market_state.price,
                        stop_loss=0,
                        take_profit=0,
                        volume=position.volume * (exit_signal.exit_percentage / 100.0),
                        risk_amount=0,
                        risk_percentage=0,
                        risk_reward_ratio=0,
                        confirmations=[exit_signal.reason],
                        market_regime="exit",
                        exit_strategy=exit_signal.urgency,
                        reasoning=exit_signal.reason,
                        timestamp=datetime.now(),
                        partial_exit=exit_signal.partial_exit,
                        exit_percentage=exit_signal.exit_percentage
                    )

                    exit_signals.append(master_exit)

                    # Update risk manager
                    trade_result = {
                        'profit': self._calculate_position_pnl(position, market_state.price),
                        'volume': position.volume,
                        'account_balance': account_info.get('balance', 0)
                    }
                    self.risk_manager.update_trade_result(trade_result)

        except Exception as e:
            logger.error(f"Error checking existing positions: {e}")

        return exit_signals

    def _validate_signal_alignment(self, professional_signal: ProfessionalSignal,
                                 entry_analysis: Dict) -> bool:
        """Validate that professional signal aligns with entry analysis"""
        try:
            # Check direction alignment
            prof_direction = professional_signal.action
            entry_direction = entry_analysis.get('direction', '')

            if prof_direction != entry_direction:
                logger.info(f"Direction mismatch: {prof_direction} vs {entry_direction}")
                return False

            # Check minimum strength requirements
            min_combined_strength = 1.0
            combined_strength = professional_signal.strength + entry_analysis.get('strength', 0)

            if combined_strength < min_combined_strength:
                logger.info(f"Combined strength too low: {combined_strength}")
                return False

            # Check minimum confirmations
            total_confirmations = len(professional_signal.confirmations) + entry_analysis.get('total_confirmations', 0)

            if total_confirmations < 5:  # Minimum 5 total confirmations
                logger.info(f"Total confirmations too low: {total_confirmations}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating signal alignment: {e}")
            return False

    def _create_master_signal(self, professional_signal: ProfessionalSignal,
                            entry_analysis: Dict,
                            position_risk: PositionRisk,
                            market_state: MarketState) -> MasterSignal:
        """Create comprehensive master signal"""
        try:
            # Combine confirmations
            all_confirmations = professional_signal.confirmations.copy()
            for conf in entry_analysis.get('confirmations', []):
                all_confirmations.append(conf.reasoning)

            # Calculate combined confidence
            combined_confidence = (professional_signal.confidence + entry_analysis.get('confidence', 0.5)) / 2

            # Create master signal
            master_signal = MasterSignal(
                action=professional_signal.action,
                strength=professional_signal.strength + entry_analysis.get('strength', 0),
                confidence=combined_confidence,
                entry_price=professional_signal.entry_price,
                stop_loss=professional_signal.stop_loss,
                take_profit=professional_signal.take_profit,
                volume=position_risk.volume,
                risk_amount=position_risk.risk_amount,
                risk_percentage=position_risk.risk_percentage,
                risk_reward_ratio=position_risk.risk_reward_ratio,
                confirmations=all_confirmations,
                market_regime=professional_signal.market_regime,
                exit_strategy="trailing_stop_with_targets",
                reasoning=f"Master Signal: {professional_signal.reasoning} | Entry confirmations: {len(entry_analysis.get('confirmations', []))}",
                timestamp=datetime.now(),
                trailing_stop_enabled=True,
                position_scaling=combined_confidence > 0.8
            )

            return master_signal

        except Exception as e:
            logger.error(f"Error creating master signal: {e}")
            return None

    def _calculate_position_pnl(self, position: PositionInfo, current_price: float) -> float:
        """Calculate position P&L"""
        try:
            if position.type == 0:  # Buy position
                return (current_price - position.price_open) * position.volume
            else:  # Sell position
                return (position.price_open - current_price) * position.volume
        except Exception as e:
            logger.error(f"Error calculating position P&L: {e}")
            return 0.0

    def get_strategy_performance(self) -> Dict:
        """Get comprehensive strategy performance metrics"""
        try:
            risk_report = self.risk_manager.get_risk_report()

            signal_efficiency = (self.signals_executed / self.total_signals_generated * 100) if self.total_signals_generated > 0 else 0

            performance = {
                'signal_statistics': {
                    'total_signals_generated': self.total_signals_generated,
                    'signals_executed': self.signals_executed,
                    'signals_rejected': self.signals_rejected,
                    'signal_efficiency': f"{signal_efficiency:.1f}%",
                    'last_signal_time': self.last_signal_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_signal_time else 'None'
                },
                'risk_metrics': risk_report,
                'strategy_status': {
                    'professional_strategy_active': True,
                    'enhanced_entry_exit_active': True,
                    'risk_management_active': True,
                    'position_management_active': True
                }
            }

            return performance

        except Exception as e:
            logger.error(f"Error getting strategy performance: {e}")
            return {}

    def update_strategy_parameters(self, new_params: Dict):
        """Update strategy parameters dynamically"""
        try:
            # Update professional strategy parameters
            if 'max_risk_per_trade' in new_params:
                self.professional_strategy.max_risk_per_trade = new_params['max_risk_per_trade']
                self.risk_manager.max_risk_per_trade = new_params['max_risk_per_trade']

            if 'min_risk_reward' in new_params:
                self.professional_strategy.min_risk_reward = new_params['min_risk_reward']
                self.risk_manager.min_risk_reward = new_params['min_risk_reward']

            # Update entry/exit parameters
            if 'min_confirmations' in new_params:
                self.entry_exit_engine.min_confirmations = new_params['min_confirmations']

            if 'profit_target_1' in new_params:
                self.entry_exit_engine.profit_target_1 = new_params['profit_target_1']

            if 'profit_target_2' in new_params:
                self.entry_exit_engine.profit_target_2 = new_params['profit_target_2']

            logger.info(f"Strategy parameters updated: {new_params}")

        except Exception as e:
            logger.error(f"Error updating strategy parameters: {e}")

    def reset_daily_metrics(self):
        """Reset daily metrics (call at start of each trading day)"""
        try:
            # Reset risk manager daily metrics
            self.risk_manager.risk_metrics.daily_risk_used = 0.0

            # Reset signal counters
            self.total_signals_generated = 0
            self.signals_executed = 0
            self.signals_rejected = 0

            logger.info("Daily metrics reset successfully")

        except Exception as e:
            logger.error(f"Error resetting daily metrics: {e}")

    def get_current_market_assessment(self, market_state: MarketState) -> Dict:
        """Get current market assessment for monitoring"""
        try:
            # Get market regime from professional strategy
            regime_analysis = self.professional_strategy._detect_market_regime(market_state)

            # Get entry opportunity analysis
            entry_analysis = self.entry_exit_engine.analyze_entry_opportunity(market_state)

            # Calculate market strength
            market_strength = 0.0
            if entry_analysis:
                market_strength = entry_analysis.get('strength', 0.0)

            assessment = {
                'market_regime': regime_analysis.get('regime', 'unknown'),
                'regime_confidence': f"{regime_analysis.get('confidence', 0.0):.1%}",
                'trend_strength': f"{regime_analysis.get('trend_strength', 0.0):.2%}",
                'volatility': f"{regime_analysis.get('volatility', 0.0):.2%}",
                'entry_opportunity': entry_analysis is not None,
                'market_strength': f"{market_strength:.2f}",
                'trading_allowed': self.risk_manager._is_trading_allowed(),
                'current_drawdown': self.risk_manager.risk_metrics.current_drawdown,
                'assessment_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return assessment

        except Exception as e:
            logger.error(f"Error getting market assessment: {e}")
            return {}
