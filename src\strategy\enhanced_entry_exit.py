"""
Enhanced Entry/Exit Logic
Cải thiện logic v<PERSON>o lệnh và thoát lệnh với multiple confirmations và trend following
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..indicators.technical_indicators import TechnicalIndicators
from ..utils.logger import setup_logger
from ..strategy.gold_strategy import TradingSignal, MarketState
from ..core.mt5_client import PositionInfo

logger = setup_logger(__name__)

@dataclass
class EntryConfirmation:
    """Entry confirmation data"""
    indicator: str
    signal: str
    strength: float
    confidence: float
    reasoning: str

@dataclass
class ExitSignal:
    """Exit signal data"""
    should_exit: bool
    reason: str
    urgency: str  # 'low', 'medium', 'high'
    partial_exit: bool
    exit_percentage: float

class EnhancedEntryExit:
    """
    Enhanced Entry/Exit Logic với:
    1. Multiple Timeframe Confirmation
    2. Volume Confirmation
    3. Momentum Confirmation
    4. Support/Resistance Levels
    5. Smart Exit Management
    """

    def __init__(self, config, mt5_client=None):
        self.config = config
        self.mt5_client = mt5_client
        self.indicators = TechnicalIndicators()

        # Entry requirements
        self.min_confirmations = 4  # Minimum confirmations needed
        self.min_total_strength = 0.7  # Minimum total signal strength

        # Exit parameters
        self.profit_target_1 = 0.015  # 1.5% first profit target
        self.profit_target_2 = 0.025  # 2.5% second profit target
        self.trailing_start = 0.02    # Start trailing at 2% profit
        self.trailing_distance = 0.01 # 1% trailing distance

    def analyze_entry_opportunity(self, market_state: MarketState,
                                timeframes: List[int] = [5, 15, 60]) -> Optional[Dict]:
        """
        Analyze entry opportunity với multiple confirmations
        """
        try:
            confirmations = []
            total_strength = 0.0

            # 1. Multi-timeframe trend analysis
            trend_confirmations = self._analyze_multi_timeframe_trend(timeframes)
            confirmations.extend(trend_confirmations)

            # 2. Momentum confirmations
            momentum_confirmations = self._analyze_momentum(market_state)
            confirmations.extend(momentum_confirmations)

            # 3. Volume confirmations
            volume_confirmations = self._analyze_volume(market_state)
            confirmations.extend(volume_confirmations)

            # 4. Support/Resistance confirmations
            sr_confirmations = self._analyze_support_resistance(market_state)
            confirmations.extend(sr_confirmations)

            # 5. Volatility confirmations
            volatility_confirmations = self._analyze_volatility(market_state)
            confirmations.extend(volatility_confirmations)

            # Calculate total strength
            total_strength = sum(c.strength for c in confirmations)

            # Check if we have enough confirmations
            if len(confirmations) < self.min_confirmations or total_strength < self.min_total_strength:
                return None

            # Determine overall signal direction
            bullish_strength = sum(c.strength for c in confirmations if 'bullish' in c.signal.lower())
            bearish_strength = sum(c.strength for c in confirmations if 'bearish' in c.signal.lower())

            if bullish_strength > bearish_strength and bullish_strength >= self.min_total_strength:
                signal_direction = 'buy'
                signal_strength = bullish_strength
            elif bearish_strength > bullish_strength and bearish_strength >= self.min_total_strength:
                signal_direction = 'sell'
                signal_strength = bearish_strength
            else:
                return None

            return {
                'direction': signal_direction,
                'strength': signal_strength,
                'confirmations': confirmations,
                'total_confirmations': len(confirmations),
                'confidence': min(1.0, signal_strength / 1.0)
            }

        except Exception as e:
            logger.error(f"Error analyzing entry opportunity: {e}")
            return None

    def _analyze_multi_timeframe_trend(self, timeframes: List[int]) -> List[EntryConfirmation]:
        """Analyze trend across multiple timeframes"""
        confirmations = []

        try:
            for tf in timeframes:
                data = self._get_data(timeframe=tf, periods=50)
                if data is None or len(data) < 20:
                    continue

                # EMA trend using pandas
                ema_fast = data['close'].ewm(span=8).mean().values
                ema_slow = data['close'].ewm(span=21).mean().values

                if len(ema_fast) < 2 or len(ema_slow) < 2:
                    continue

                # Current trend
                if ema_fast[-1] > ema_slow[-1]:
                    trend = 'bullish'
                    strength = min(0.2, abs(ema_fast[-1] - ema_slow[-1]) / data['close'].iloc[-1] * 10)
                else:
                    trend = 'bearish'
                    strength = min(0.2, abs(ema_fast[-1] - ema_slow[-1]) / data['close'].iloc[-1] * 10)

                # Trend consistency
                consistency = 0
                for i in range(-5, 0):  # Check last 5 periods
                    if i < -len(ema_fast) or i < -len(ema_slow):
                        continue
                    if (ema_fast[i] > ema_slow[i]) == (trend == 'bullish'):
                        consistency += 1

                confidence = consistency / 5.0

                confirmations.append(EntryConfirmation(
                    indicator=f'EMA_Trend_TF{tf}',
                    signal=f'{trend}_trend',
                    strength=strength * confidence,
                    confidence=confidence,
                    reasoning=f'TF{tf}: EMA trend {trend} with {confidence:.1%} consistency'
                ))

        except Exception as e:
            logger.error(f"Error analyzing multi-timeframe trend: {e}")

        return confirmations

    def _analyze_momentum(self, market_state: MarketState) -> List[EntryConfirmation]:
        """Analyze momentum indicators"""
        confirmations = []

        try:
            # MACD momentum
            if hasattr(market_state, 'macd_signal'):
                macd = market_state.macd_signal

                if macd.crossover == 'bullish_cross':
                    confirmations.append(EntryConfirmation(
                        indicator='MACD',
                        signal='bullish_crossover',
                        strength=0.25,
                        confidence=0.8,
                        reasoning='MACD bullish crossover detected'
                    ))
                elif macd.crossover == 'bearish_cross':
                    confirmations.append(EntryConfirmation(
                        indicator='MACD',
                        signal='bearish_crossover',
                        strength=0.25,
                        confidence=0.8,
                        reasoning='MACD bearish crossover detected'
                    ))
                elif macd.trend == 'bullish' and macd.histogram > 0:
                    confirmations.append(EntryConfirmation(
                        indicator='MACD',
                        signal='bullish_momentum',
                        strength=0.15,
                        confidence=0.6,
                        reasoning='MACD showing bullish momentum'
                    ))
                elif macd.trend == 'bearish' and macd.histogram < 0:
                    confirmations.append(EntryConfirmation(
                        indicator='MACD',
                        signal='bearish_momentum',
                        strength=0.15,
                        confidence=0.6,
                        reasoning='MACD showing bearish momentum'
                    ))

            # RSI momentum
            if hasattr(market_state, 'rsi_data'):
                rsi = market_state.rsi_data.rsi

                if 40 < rsi < 60:  # Neutral zone - good for trend following
                    if hasattr(market_state, 'macd_signal') and market_state.macd_signal.trend == 'bullish':
                        confirmations.append(EntryConfirmation(
                            indicator='RSI',
                            signal='bullish_neutral',
                            strength=0.1,
                            confidence=0.7,
                            reasoning='RSI in neutral zone supporting bullish trend'
                        ))
                    elif hasattr(market_state, 'macd_signal') and market_state.macd_signal.trend == 'bearish':
                        confirmations.append(EntryConfirmation(
                            indicator='RSI',
                            signal='bearish_neutral',
                            strength=0.1,
                            confidence=0.7,
                            reasoning='RSI in neutral zone supporting bearish trend'
                        ))

        except Exception as e:
            logger.error(f"Error analyzing momentum: {e}")

        return confirmations

    def _analyze_volume(self, market_state: MarketState) -> List[EntryConfirmation]:
        """Analyze volume confirmations"""
        confirmations = []

        try:
            # Get recent volume data
            data = self._get_data(periods=20)
            if data is None or 'tick_volume' not in data.columns:
                return confirmations

            current_volume = data['tick_volume'].iloc[-1]
            avg_volume = data['tick_volume'].rolling(10).mean().iloc[-1]

            # Volume surge confirmation
            if current_volume > avg_volume * 1.5:
                # Determine direction based on price action
                price_change = (data['close'].iloc[-1] - data['close'].iloc[-2]) / data['close'].iloc[-2]

                if price_change > 0.001:  # Positive price change with volume
                    confirmations.append(EntryConfirmation(
                        indicator='Volume',
                        signal='bullish_volume_surge',
                        strength=0.15,
                        confidence=0.7,
                        reasoning=f'Volume surge ({current_volume/avg_volume:.1f}x) with bullish price action'
                    ))
                elif price_change < -0.001:  # Negative price change with volume
                    confirmations.append(EntryConfirmation(
                        indicator='Volume',
                        signal='bearish_volume_surge',
                        strength=0.15,
                        confidence=0.7,
                        reasoning=f'Volume surge ({current_volume/avg_volume:.1f}x) with bearish price action'
                    ))

        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")

        return confirmations

    def _analyze_support_resistance(self, market_state: MarketState) -> List[EntryConfirmation]:
        """Analyze support/resistance levels"""
        confirmations = []

        try:
            if hasattr(market_state, 'pivot_points'):
                pivot = market_state.pivot_points
                current_price = market_state.price

                # Distance from pivot levels
                pivot_distance = abs(current_price - pivot.pivot) / current_price

                # Near support with bullish signal
                if current_price < pivot.pivot and pivot_distance < 0.002:  # Within 0.2%
                    confirmations.append(EntryConfirmation(
                        indicator='Support_Resistance',
                        signal='bullish_support',
                        strength=0.2,
                        confidence=0.8,
                        reasoning='Price near pivot support level'
                    ))

                # Near resistance with bearish signal
                elif current_price > pivot.pivot and pivot_distance < 0.002:  # Within 0.2%
                    confirmations.append(EntryConfirmation(
                        indicator='Support_Resistance',
                        signal='bearish_resistance',
                        strength=0.2,
                        confidence=0.8,
                        reasoning='Price near pivot resistance level'
                    ))

        except Exception as e:
            logger.error(f"Error analyzing support/resistance: {e}")

        return confirmations

    def _analyze_volatility(self, market_state: MarketState) -> List[EntryConfirmation]:
        """Analyze volatility conditions"""
        confirmations = []

        try:
            if hasattr(market_state, 'atr_data'):
                atr = market_state.atr_data
                current_price = market_state.price

                # Volatility level
                volatility_pct = atr.atr / current_price

                # Optimal volatility for trading (not too low, not too high)
                if 0.01 < volatility_pct < 0.03:  # 1% to 3%
                    confirmations.append(EntryConfirmation(
                        indicator='Volatility',
                        signal='optimal_volatility',
                        strength=0.1,
                        confidence=0.6,
                        reasoning=f'Optimal volatility level ({volatility_pct:.1%})'
                    ))

        except Exception as e:
            logger.error(f"Error analyzing volatility: {e}")

        return confirmations

    def _get_data(self, timeframe: int = 5, periods: int = 100) -> Optional[pd.DataFrame]:
        """Get market data for analysis"""
        try:
            if self.mt5_client:
                return self.mt5_client.get_historical_data(
                    symbol=self.config.mt5.symbol,
                    timeframe=timeframe,
                    count=periods
                )
            return None
        except Exception as e:
            logger.error(f"Error getting data: {e}")
            return None

    def analyze_exit_opportunity(self, position: PositionInfo,
                               market_state: MarketState,
                               account_info: Dict) -> ExitSignal:
        """
        Analyze exit opportunity với smart exit management
        """
        try:
            current_price = market_state.price
            entry_price = position.price_open
            position_type = position.type  # 0 = buy, 1 = sell

            # Calculate current P&L
            if position_type == 0:  # Buy position
                pnl_pct = (current_price - entry_price) / entry_price
            else:  # Sell position
                pnl_pct = (entry_price - current_price) / entry_price

            # 1. Check stop loss
            if self._check_stop_loss(position, current_price, market_state):
                return ExitSignal(
                    should_exit=True,
                    reason="Stop loss triggered",
                    urgency="high",
                    partial_exit=False,
                    exit_percentage=100.0
                )

            # 2. Check profit targets
            profit_exit = self._check_profit_targets(position, current_price, pnl_pct)
            if profit_exit.should_exit:
                return profit_exit

            # 3. Check trailing stop
            trailing_exit = self._check_trailing_stop(position, current_price, pnl_pct)
            if trailing_exit.should_exit:
                return trailing_exit

            # 4. Check time-based exit
            time_exit = self._check_time_based_exit(position)
            if time_exit.should_exit:
                return time_exit

            # 5. Check technical reversal signals
            reversal_exit = self._check_reversal_signals(position, market_state)
            if reversal_exit.should_exit:
                return reversal_exit

            # 6. Check market condition changes
            condition_exit = self._check_market_condition_exit(position, market_state)
            if condition_exit.should_exit:
                return condition_exit

            # No exit signal
            return ExitSignal(
                should_exit=False,
                reason="Position maintained",
                urgency="low",
                partial_exit=False,
                exit_percentage=0.0
            )

        except Exception as e:
            logger.error(f"Error analyzing exit opportunity: {e}")
            return ExitSignal(
                should_exit=False,
                reason="Error in exit analysis",
                urgency="low",
                partial_exit=False,
                exit_percentage=0.0
            )

    def _check_stop_loss(self, position: PositionInfo, current_price: float,
                        market_state: MarketState) -> bool:
        """Check if stop loss should be triggered"""
        try:
            # Use ATR-based dynamic stop loss
            if hasattr(market_state, 'atr_data'):
                atr_value = market_state.atr_data.atr
                stop_multiplier = 2.0  # Conservative stop loss

                if position.type == 0:  # Buy position
                    dynamic_stop = position.price_open - (atr_value * stop_multiplier)
                    return current_price <= dynamic_stop
                else:  # Sell position
                    dynamic_stop = position.price_open + (atr_value * stop_multiplier)
                    return current_price >= dynamic_stop

            # Fallback to percentage-based stop loss
            stop_loss_pct = 0.02  # 2% stop loss

            if position.type == 0:  # Buy position
                return current_price <= position.price_open * (1 - stop_loss_pct)
            else:  # Sell position
                return current_price >= position.price_open * (1 + stop_loss_pct)

        except Exception as e:
            logger.error(f"Error checking stop loss: {e}")
            return False

    def _check_profit_targets(self, position: PositionInfo, current_price: float,
                            pnl_pct: float) -> ExitSignal:
        """Check profit targets for partial exits"""
        try:
            # First profit target - take 50% profit
            if pnl_pct >= self.profit_target_1:
                return ExitSignal(
                    should_exit=True,
                    reason=f"First profit target reached ({pnl_pct:.1%})",
                    urgency="medium",
                    partial_exit=True,
                    exit_percentage=50.0
                )

            # Second profit target - take remaining 50%
            if pnl_pct >= self.profit_target_2:
                return ExitSignal(
                    should_exit=True,
                    reason=f"Second profit target reached ({pnl_pct:.1%})",
                    urgency="medium",
                    partial_exit=True,
                    exit_percentage=50.0
                )

            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

        except Exception as e:
            logger.error(f"Error checking profit targets: {e}")
            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

    def _check_trailing_stop(self, position: PositionInfo, current_price: float,
                           pnl_pct: float) -> ExitSignal:
        """Check trailing stop logic"""
        try:
            # Only apply trailing stop if we're in profit above threshold
            if pnl_pct < self.trailing_start:
                return ExitSignal(should_exit=False, reason="", urgency="low",
                                partial_exit=False, exit_percentage=0.0)

            # Calculate trailing stop level
            if position.type == 0:  # Buy position
                trailing_stop = current_price * (1 - self.trailing_distance)
                if current_price <= trailing_stop:
                    return ExitSignal(
                        should_exit=True,
                        reason=f"Trailing stop triggered at {pnl_pct:.1%} profit",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )
            else:  # Sell position
                trailing_stop = current_price * (1 + self.trailing_distance)
                if current_price >= trailing_stop:
                    return ExitSignal(
                        should_exit=True,
                        reason=f"Trailing stop triggered at {pnl_pct:.1%} profit",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )

            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

        except Exception as e:
            logger.error(f"Error checking trailing stop: {e}")
            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

    def _check_time_based_exit(self, position: PositionInfo) -> ExitSignal:
        """Check time-based exit conditions"""
        try:
            position_age = datetime.now() - position.time
            max_hold_time = timedelta(hours=8)  # Maximum 8 hours

            if position_age > max_hold_time:
                return ExitSignal(
                    should_exit=True,
                    reason=f"Maximum hold time exceeded ({position_age})",
                    urgency="medium",
                    partial_exit=False,
                    exit_percentage=100.0
                )

            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

        except Exception as e:
            logger.error(f"Error checking time-based exit: {e}")
            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

    def _check_reversal_signals(self, position: PositionInfo,
                              market_state: MarketState) -> ExitSignal:
        """Check for technical reversal signals"""
        try:
            # MACD reversal
            if hasattr(market_state, 'macd_signal'):
                macd = market_state.macd_signal

                # Exit long position on bearish MACD crossover
                if position.type == 0 and macd.crossover == 'bearish_cross':
                    return ExitSignal(
                        should_exit=True,
                        reason="MACD bearish crossover - trend reversal",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )

                # Exit short position on bullish MACD crossover
                if position.type == 1 and macd.crossover == 'bullish_cross':
                    return ExitSignal(
                        should_exit=True,
                        reason="MACD bullish crossover - trend reversal",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )

            # RSI extreme reversal
            if hasattr(market_state, 'rsi_data'):
                rsi = market_state.rsi_data.rsi

                # Exit long position if RSI becomes extremely overbought
                if position.type == 0 and rsi > 80:
                    return ExitSignal(
                        should_exit=True,
                        reason="RSI extremely overbought - potential reversal",
                        urgency="medium",
                        partial_exit=True,
                        exit_percentage=50.0
                    )

                # Exit short position if RSI becomes extremely oversold
                if position.type == 1 and rsi < 20:
                    return ExitSignal(
                        should_exit=True,
                        reason="RSI extremely oversold - potential reversal",
                        urgency="medium",
                        partial_exit=True,
                        exit_percentage=50.0
                    )

            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

        except Exception as e:
            logger.error(f"Error checking reversal signals: {e}")
            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

    def _check_market_condition_exit(self, position: PositionInfo,
                                   market_state: MarketState) -> ExitSignal:
        """Check for market condition changes requiring exit"""
        try:
            # High volatility exit
            if hasattr(market_state, 'atr_data'):
                atr = market_state.atr_data
                current_price = market_state.price
                volatility_pct = atr.atr / current_price

                # Exit if volatility spikes above 5%
                if volatility_pct > 0.05:
                    return ExitSignal(
                        should_exit=True,
                        reason=f"High volatility spike ({volatility_pct:.1%})",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )

            # Spread widening exit
            if hasattr(market_state, 'spread'):
                max_spread = getattr(self.config.risk, 'max_spread', 200)
                if market_state.spread > max_spread * 2:  # Double the normal max spread
                    return ExitSignal(
                        should_exit=True,
                        reason=f"Excessive spread widening ({market_state.spread})",
                        urgency="high",
                        partial_exit=False,
                        exit_percentage=100.0
                    )

            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)

        except Exception as e:
            logger.error(f"Error checking market condition exit: {e}")
            return ExitSignal(should_exit=False, reason="", urgency="low",
                            partial_exit=False, exit_percentage=0.0)
